<?php
defined('BASEPATH') or exit('No direct script access allowed');

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

/**
 * Create By : Aryo
 * Youtube : Aryo Coding
 */
class Lap_tb extends MY_Controller
{

  function __construct()
  {
    parent::__construct();
    $this->load->model(array('Mod_laporan'));
    // $this->load->model('dashboard/Mod_dashboard');
  }

  public function index()
  {
    $link = 'Lap_tb';
    $level = $this->session->userdata['id_level'];
    // Cek Posisi Menu apakah Sub Menu Atau bukan
    $jml = $this->Mod_dashboard->get_akses_menu($link, $level)->num_rows();

    if ($jml > 0) { //Jika Menu
      $data['akses_menu'] = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
      $a_menu = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
      $akses = $a_menu->view;
    } else {
      $data['akses_menu'] = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
      $a_submenu = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
      $akses = $a_submenu->view;
    }

    if ($akses == "Y") {
      $this->template->load('layoutbackend', 'laporan/lap_tb', $data);
    } else {
      $data['page'] = $link;
      $this->template->load('layoutbackend', 'admin/akses_ditolak', $data);
    }
  }
  public function laporan()
  {
    $id_supplier = $this->input->post('supplier');
    $tglrange = $this->input->post('tgl');
    $faktur = $this->input->post('faktur');
    $data['act'] = "";
    $data['lap'] = $this->Mod_laporan->get_laporan_tb($id_supplier, $tglrange, $faktur);
    $this->load->view('laporan/vlap_tb', $data);
  }

  public function cetak()
  {
    $id_supplier = $this->input->post('supplier');
    $tglrange = $this->input->post('tgl');
    $faktur = $this->input->post('faktur');
    $data['act'] = "";
    $data['tgltange'] = $this->input->post('tgl');
    $data['lap'] = $this->Mod_laporan->get_laporan_tb($id_supplier, $tglrange, $faktur);
    $this->load->view('laporan/cetak_tb', $data);
  }


  public function lap_excel()
  {
    $id_supplier = $this->input->post('supplier');
    $tglrange = $this->input->post('tgl');
    $faktur = $this->input->post('faktur');
    $list = $this->Mod_laporan->get_laporan_tb($id_supplier, $tglrange, $faktur)->result();
    $spreadsheet = new Spreadsheet();
    $sheet = $spreadsheet->getActiveSheet();
    $sheet->mergeCells('A1:I1');
    $sheet->setCellValue('A1', 'Laporan Periode: ' . $tglrange);

    $sheet->setCellValue('A2', 'No');
    $sheet->setCellValue('B2', 'No TB');
    $sheet->setCellValue('C2', 'Tanggal');
    $sheet->setCellValue('D2', 'Supplier');
    $sheet->setCellValue('E2', 'Nama Barang');
    $sheet->setCellValue('F2', 'Satuan');
    $sheet->setCellValue('G2', 'Jumlah');
    $sheet->setCellValue('H2', 'Harga');
    $sheet->setCellValue('I2', 'Subtotal');
    $no = 1;
    $x = 3;
    foreach ($list as $row) {
      $subt = ($row->harga * $row->jumlah);
      $sheet->setCellValue('A' . $x, $no++);
      $sheet->setCellValue('B' . $x, $row->faktur);
      $sheet->setCellValue('C' . $x, date("d/m/Y", strtotime($row->tanggal)));
      $sheet->setCellValue('D' . $x, $row->nama_supplier);
      $sheet->setCellValue('E' . $x, $row->nama_barang);
      $sheet->setCellValue('F' . $x, $row->nama_kemasan);
      $sheet->setCellValue('G' . $x, $row->jumlah);
      $sheet->setCellValue('H' . $x, $row->harga);
      $sheet->setCellValue('I' . $x, $subt);
      $sheet->getStyle('H' . $x)->getNumberFormat()->setFormatCode('"Rp"#,##0');
      $sheet->getStyle('I' . $x)->getNumberFormat()->setFormatCode('"Rp"#,##0');
      $x++;
    }
    $writer = new Xlsx($spreadsheet);
    $filename = 'laporan_barang_masuk';

    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment;filename="' . $filename . '.xls"');
    header('Cache-Control: max-age=0');

    $writer->save('php://output');
  }

  public function get_faktur()
  {
    $faktur = $this->input->get('term');
    $data = $this->Mod_laporan->get_sbbk_masuk($faktur);
    if (count($data) > 0) {

      foreach ($data as $row) {
        $arr_result[] = array('value' => $row->id, 'label'  => $row->faktur, 'supplier' => $row->id_supplier, 'nama_supplier' => $row->nama_supplier);
      }
      echo json_encode($arr_result);
    } else {
      $arr_result = array('label'  => "Data Tidak di Temukan");
      echo json_encode($arr_result);
    }
  }
}
