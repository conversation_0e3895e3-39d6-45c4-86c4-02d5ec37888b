<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Gudang extends MY_Controller
{
    function __construct()
    {
        parent::__construct();
        // Load required models
        $this->load->model('Mod_gudang');
    }

    public function index()
    {
        $link = 'gudang';
        $level = $this->session->userdata('id_level');

        $jml = $this->Mod_dashboard->get_akses_menu($link, $level)->num_rows();
        if ($jml > 0) {
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $akses = $data['akses_menu']->view;
        } else {
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $akses = $data['akses_menu']->view;
        }

        if ($akses == 'Y') {
            $this->template->load('layoutbackend', 'gudang/gudang', $data);
        } else {
            $data['page'] = $link;
            $this->template->load('layoutbackend', 'admin/akses_ditolak', $data);
        }
    }

    public function ajax_list()
    {
        ini_set('memory_limit', '512M');
        set_time_limit(3600);
        $list = $this->Mod_gudang->get_datatables();
        $data = array();
        $no = $_POST['start'];
        foreach ($list as $row) {
            $no++;
            $sub_array = array();
            $sub_array[] = $row->nama;

            $sub_array[] = "<a class=\"btn btn-xs btn-outline-primary edit\" href=\"javascript:void(0)\" title=\"Edit\" onclick=\"edit('$row->id')\"><i class=\"fas fa-edit\"></i></a>
                             <a class=\"btn btn-xs btn-outline-danger delete\" href=\"javascript:void(0)\" title=\"Delete\" onclick=\"hapus('$row->id')\"><i class=\"fas fa-trash\"></i></a>";
            $data[] = $sub_array;
        }

        $output = array(
            "draw" => $_POST['draw'],
            "recordsTotal" => $this->Mod_gudang->count_all(),
            "recordsFiltered" => $this->Mod_gudang->count_filtered(),
            "data" => $data,
        );
        echo json_encode($output);
    }

    public function form_input()
    {
        $this->load->view('gudang/form_input');
    }
    public function insert()
    {
        $save = array(
            'nama' => $this->input->post('nama'),

        );
        $this->Mod_gudang->insert('gudang', $save);
        echo json_encode(array("status" => TRUE));
    }

    public function update()
    {
        $id = $this->input->post('id');
        $save = array(
            'nama' => $this->input->post('nama'),

        );
        $this->Mod_gudang->update($id, $save);
        echo json_encode(array("status" => TRUE));
    }

    public function edit($id)
    {
        $data = $this->Mod_gudang->get($id);
        echo json_encode($data);
    }

    public function delete()
    {
        $id = $this->input->post('id');
        $this->Mod_gudang->delete($id, 'gudang');
        echo json_encode(array("status" => TRUE));
    }

    private function _validate()
    {
        $data = array();
        $data['error_string'] = array();
        $data['inputerror'] = array();
        $data['status'] = TRUE;

        if ($this->input->post('nama') == '') {
            $data['inputerror'][] = 'nama';
            $data['error_string'][] = 'Nama Tidak Boleh Kosong';
            $data['status'] = FALSE;
        }
        if ($data['status'] === FALSE) {
            echo json_encode($data);
            exit();
        }
    }
}
