<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Orders extends MY_Controller
{
    function __construct()
    {
        parent::__construct();
        $this->load->model('Mod_orders');
    }

    public function index()
    {
        $link = 'orders';
        $level = $this->session->userdata('id_level');

        $jml = $this->Mod_dashboard->get_akses_menu($link, $level)->num_rows();
        if ($jml > 0) {
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $akses = $data['akses_menu']->view;
        } else {
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $akses = $data['akses_menu']->view;
        }

        if ($akses == 'Y') {
            $this->template->load('layoutbackend', 'orders/orders', $data);
        } else {
            $data['page'] = $link;
            $this->template->load('layoutbackend', 'admin/akses_ditolak', $data);
        }
    }

    public function ajax_list()
    {
        ini_set('memory_limit', '512M');
        set_time_limit(3600);
        $list = $this->Mod_orders->get_datatables();
        $data = array();
        $no = $_POST['start'];
        foreach ($list as $row) {
            $no++;
            $sub_array = array();
            $sub_array[] = $row->order_no;
            $sub_array[] = date('d-m-Y', strtotime($row->order_date));
            $sub_array[] = $row->customer_name ? $row->customer_name : '-';

            // Status badge
            $status_class = $this->getStatusClass($row->order_status);
            $sub_array[] = "<span class=\"badge badge-{$status_class}\">" . ucfirst($row->order_status) . "</span>";

            // Priority badge
            $priority_class = $this->getPriorityClass($row->priority);
            $sub_array[] = "<span class=\"badge badge-{$priority_class}\">" . ucfirst($row->priority) . "</span>";

            // Payment status
            $payment_class = $this->getPaymentStatusClass($row->payment_status);
            $sub_array[] = "<span class=\"badge badge-{$payment_class}\">" . ucfirst($row->payment_status) . "</span>";

            $sub_array[] = 'Rp ' . number_format($row->total_amount, 0, ',', '.');

            $sub_array[] = "<a class=\"btn btn-xs btn-outline-info detail\" href=\"javascript:void(0)\" title=\"Detail\" onclick=\"detail('$row->id')\"><i class=\"fas fa-list\"></i></a>
                             <a class=\"btn btn-xs btn-outline-primary edit\" href=\"javascript:void(0)\" title=\"Edit\" onclick=\"edit('$row->id')\"><i class=\"fas fa-edit\"></i></a>
                             <a class=\"btn btn-xs btn-outline-danger delete\" href=\"javascript:void(0)\" title=\"Delete\" onclick=\"hapus('$row->id')\"><i class=\"fas fa-trash\"></i></a>";
            $data[] = $sub_array;
        }

        $output = array(
            "draw" => $_POST['draw'],
            "recordsTotal" => $this->Mod_orders->count_all(),
            "recordsFiltered" => $this->Mod_orders->count_filtered(),
            "data" => $data,
        );
        echo json_encode($output);
    }

    public function form_input()
    {
        $this->load->view('orders/form_input');
    }
    public function insert()
    {
        $save = array(
            'order_no' => $this->input->post('order_no'),
            'order_date' => $this->input->post('order_date'),
            'order_notes' => $this->input->post('order_notes'),
            'customer_name' => $this->input->post('customer_name'),
            'customer_phone' => $this->input->post('customer_phone'),
            'customer_email' => $this->input->post('customer_email'),
            'customer_address' => $this->input->post('customer_address'),
            'order_status' => $this->input->post('order_status') ? $this->input->post('order_status') : 'draft',
            'priority' => $this->input->post('priority') ? $this->input->post('priority') : 'normal',
            'due_date' => $this->input->post('due_date'),
            'delivery_date' => $this->input->post('delivery_date'),
            'payment_status' => $this->input->post('payment_status') ? $this->input->post('payment_status') : 'unpaid',
            'payment_method' => $this->input->post('payment_method'),
            'discount_type' => $this->input->post('discount_type') ? $this->input->post('discount_type') : 'none',
            'discount_value' => $this->input->post('discount_value') ? $this->input->post('discount_value') : 0,
            'tax_percentage' => $this->input->post('tax_percentage') ? $this->input->post('tax_percentage') : 0,
            'shipping_cost' => $this->input->post('shipping_cost') ? $this->input->post('shipping_cost') : 0,
            'internal_notes' => $this->input->post('internal_notes'),
            'created_by' => $this->session->userdata('id_user'),
        );
        $this->Mod_orders->insert('orders', $save);
        echo json_encode(array("status" => TRUE));
    }

    public function update()
    {
        $id = $this->input->post('id');
        $save = array(
            'order_no' => $this->input->post('order_no'),
            'order_date' => $this->input->post('order_date'),
            'order_notes' => $this->input->post('order_notes'),
            'customer_name' => $this->input->post('customer_name'),
            'customer_phone' => $this->input->post('customer_phone'),
            'customer_email' => $this->input->post('customer_email'),
            'customer_address' => $this->input->post('customer_address'),
            'order_status' => $this->input->post('order_status'),
            'priority' => $this->input->post('priority'),
            'due_date' => $this->input->post('due_date'),
            'delivery_date' => $this->input->post('delivery_date'),
            'payment_status' => $this->input->post('payment_status'),
            'payment_method' => $this->input->post('payment_method'),
            'discount_type' => $this->input->post('discount_type'),
            'discount_value' => $this->input->post('discount_value'),
            'tax_percentage' => $this->input->post('tax_percentage'),
            'shipping_cost' => $this->input->post('shipping_cost'),
            'internal_notes' => $this->input->post('internal_notes'),
            'updated_by' => $this->session->userdata('id_user'),
        );
        $this->Mod_orders->update($id, $save);
        echo json_encode(array("status" => TRUE));
    }

    public function edit($id)
    {
        $data = $this->Mod_orders->get($id);
        echo json_encode($data);
    }

    public function delete()
    {
        $id = $this->input->post('id');
        $this->Mod_orders->delete($id, 'orders');
        echo json_encode(array("status" => TRUE));
    }

    public function detail($id)
    {
        $link = 'orders';
        $level = $this->session->userdata('id_level');

        $jml = $this->Mod_dashboard->get_akses_menu($link, $level)->num_rows();
        if ($jml > 0) {
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $akses = $data['akses_menu']->view;
        } else {
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $akses = $data['akses_menu']->view;
        }

        if ($akses == 'Y') {
            $data['order'] = $this->Mod_orders->get($id);
            $data['order_id'] = $id;
            $this->template->load('layoutbackend', 'orders/order_detail', $data);
        } else {
            $data['page'] = $link;
            $this->template->load('layoutbackend', 'admin/akses_ditolak', $data);
        }
    }

    public function ajax_list_detail()
    {
        $order_id = $this->input->post('order_id');
        $list = $this->Mod_orders->get_detail_datatables($order_id);
        $data = array();
        $no = $_POST['start'];
        foreach ($list as $row) {
            $no++;
            $sub_array = array();
            $sub_array[] = $row->nama_barang;
            $sub_array[] = $row->nama_satuan;
            $sub_array[] = number_format($row->qty, 0);
            $sub_array[] = 'Rp ' . number_format($row->harga, 0);
            $sub_array[] = 'Rp ' . number_format($row->qty * $row->harga, 0);
            $sub_array[] = $row->keterangan;

            $sub_array[] = "<button class=\"action-btn edit\" title=\"Edit\" onclick=\"editDetail('$row->id')\"><i class=\"fas fa-edit\"></i></button>
                             <button class=\"action-btn delete\" title=\"Delete\" onclick=\"hapusDetail('$row->id')\"><i class=\"fas fa-trash\"></i></button>";
            $data[] = $sub_array;
        }

        $output = array(
            "draw" => $_POST['draw'],
            "recordsTotal" => $this->Mod_orders->count_all_detail($order_id),
            "recordsFiltered" => $this->Mod_orders->count_filtered_detail($order_id),
            "data" => $data,
        );
        echo json_encode($output);
    }

    public function form_input_detail()
    {
        $this->load->view('orders/form_input_detail');
    }

    public function insert_detail()
    {
        try {
            $this->_validate_detail();
            $save = array(
                'order_id' => $this->input->post('order_id'),
                'id_barang' => $this->input->post('id_barang'),
                'qty' => $this->input->post('qty'),
                'harga' => $this->input->post('harga'),
                'keterangan' => $this->input->post('keterangan'),
            );
            $result = $this->Mod_orders->insert('order_detail', $save);
            if ($result) {
                echo json_encode(array("status" => TRUE));
            } else {
                echo json_encode(array("status" => FALSE, "pesan" => "Gagal menyimpan data"));
            }
        } catch (Exception $e) {
            echo json_encode(array("status" => FALSE, "pesan" => "Error: " . $e->getMessage()));
        }
    }

    public function update_detail()
    {
        $this->_validate_detail();
        $id = $this->input->post('id');
        $save = array(
            'id_barang' => $this->input->post('id_barang'),
            'qty' => $this->input->post('qty'),
            'harga' => $this->input->post('harga'),
            'keterangan' => $this->input->post('keterangan'),
        );
        $this->Mod_orders->update_detail($id, $save);
        echo json_encode(array("status" => TRUE));
    }

    public function edit_detail($id)
    {
        $data = $this->Mod_orders->get_detail($id);
        echo json_encode($data);
    }

    public function delete_detail()
    {
        $id = $this->input->post('id');
        $this->Mod_orders->delete($id, 'order_detail');
        echo json_encode(array("status" => TRUE));
    }

    public function get_barang()
    {
        $term = $this->input->get('term');
        if (empty($term)) {
            echo json_encode(array());
            return;
        }

        $data = $this->Mod_orders->get_barang($term);
        $arr_result = array();

        if (count($data) > 0) {
            foreach ($data as $row) {
                $arr_result[] = array(
                    'label' => $row->nama,
                    'value' => $row->id,
                    'nama_barang' => $row->nama,
                    'harga' => $row->harga,
                    'id_satuan' => $row->id_satuan,
                    'nama_satuan' => $row->nama_satuan ? $row->nama_satuan : 'Pcs'
                );
            }
        } else {
            $arr_result[] = array(
                'label' => "Data Tidak di Temukan",
                'value' => '',
                'nama_barang' => '',
                'harga' => 0,
                'id_satuan' => '',
                'nama_satuan' => ''
            );
        }

        echo json_encode($arr_result);
    }

    private function _validate()
    {
        $data = array();
        $data['error_string'] = array();
        $data['inputerror'] = array();
        $data['status'] = TRUE;

        if ($this->input->post('order_no') == '') {
            $data['inputerror'][] = 'order_no';
            $data['error_string'][] = 'Order_no Tidak Boleh Kosong';
            $data['status'] = FALSE;
        }

        if ($this->input->post('order_date') == '') {
            $data['inputerror'][] = 'order_date';
            $data['error_string'][] = 'Order_date Tidak Boleh Kosong';
            $data['status'] = FALSE;
        }

        if ($this->input->post('order_notes') == '') {
            $data['inputerror'][] = 'order_notes';
            $data['error_string'][] = 'Order_notes Tidak Boleh Kosong';
            $data['status'] = FALSE;
        }
        if ($data['status'] === FALSE) {
            echo json_encode($data);
            exit();
        }
    }

    public function get_order_summary()
    {
        $order_id = $this->input->post('order_id');
        $summary = $this->Mod_orders->get_order_summary($order_id);
        echo json_encode($summary);
    }

    public function calculate_total()
    {
        $order_id = $this->input->post('order_id');
        $order = $this->Mod_orders->get($order_id);
        $summary = $this->Mod_orders->get_order_summary($order_id);

        $subtotal = $summary['grand_total'];
        $discount_amount = 0;

        // Calculate discount
        if ($order->discount_type == 'percentage') {
            $discount_amount = ($subtotal * $order->discount_value) / 100;
        } elseif ($order->discount_type == 'fixed') {
            $discount_amount = $order->discount_value;
        }

        $after_discount = $subtotal - $discount_amount;
        $tax_amount = ($after_discount * $order->tax_percentage) / 100;
        $total_amount = $after_discount + $tax_amount + $order->shipping_cost;

        // Update total amount in database
        $this->Mod_orders->update($order_id, array('total_amount' => $total_amount));

        echo json_encode(array(
            'subtotal' => $subtotal,
            'discount_amount' => $discount_amount,
            'after_discount' => $after_discount,
            'tax_amount' => $tax_amount,
            'shipping_cost' => $order->shipping_cost,
            'total_amount' => $total_amount
        ));
    }

    private function _validate_detail()
    {
        $data = array();
        $data['error_string'] = array();
        $data['inputerror'] = array();
        $data['status'] = TRUE;

        if ($this->input->post('order_id') == '' || $this->input->post('order_id') <= 0) {
            $data['inputerror'][] = 'order_id';
            $data['error_string'][] = 'Order ID Tidak Valid';
            $data['status'] = FALSE;
        }

        if ($this->input->post('id_barang') == '' || $this->input->post('id_barang') <= 0) {
            $data['inputerror'][] = 'id_barang';
            $data['error_string'][] = 'Barang Tidak Boleh Kosong';
            $data['status'] = FALSE;
        }

        if ($this->input->post('qty') == '' || $this->input->post('qty') <= 0) {
            $data['inputerror'][] = 'qty';
            $data['error_string'][] = 'Qty Harus Lebih Dari 0';
            $data['status'] = FALSE;
        }

        if ($this->input->post('harga') == '' || $this->input->post('harga') < 0) {
            $data['inputerror'][] = 'harga';
            $data['error_string'][] = 'Harga Tidak Boleh Kosong';
            $data['status'] = FALSE;
        }

        if ($data['status'] === FALSE) {
            echo json_encode($data);
            exit();
        }
    }

    private function getStatusClass($status)
    {
        switch ($status) {
            case 'draft': return 'secondary';
            case 'pending': return 'warning';
            case 'confirmed': return 'info';
            case 'processing': return 'primary';
            case 'shipped': return 'light';
            case 'delivered': return 'success';
            case 'completed': return 'success';
            case 'cancelled': return 'danger';
            default: return 'secondary';
        }
    }

    private function getPriorityClass($priority)
    {
        switch ($priority) {
            case 'low': return 'success';
            case 'normal': return 'info';
            case 'high': return 'warning';
            case 'urgent': return 'danger';
            default: return 'info';
        }
    }

    private function getPaymentStatusClass($status)
    {
        switch ($status) {
            case 'unpaid': return 'danger';
            case 'partial': return 'warning';
            case 'paid': return 'success';
            case 'refunded': return 'secondary';
            default: return 'secondary';
        }
    }
}
