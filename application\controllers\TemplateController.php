<?php
defined('BASEPATH') or exit('No direct script access allowed');

class TemplateController extends MY_Controller
{

    public function __construct()
    {
        parent::__construct();
        $this->load->dbforge();
        $this->load->database();
    }

    public function index()
    {
        $link = $this->uri->segment(1);
        $level = $this->session->userdata('id_level');

        // Cek Posisi Menu apakah Sub Menu Atau bukan
        $jml = $this->Mod_dashboard->get_akses_menu($link, $level)->num_rows();

        if ($jml > 0) { //Jika Menu
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $a_menu = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $akses = $a_menu->view;
        } else {
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $a_submenu = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $akses = $a_submenu->view;
        }
        if ($akses == "Y") {
            $this->template->load('layoutbackend', 'template_view', $data);
        } else {
            $data['page'] = $link;
            $this->template->load('layoutbackend', 'admin/akses_ditolak', $data);
        }
    }
    // $this->generate($table_name, $fields);
    // public function create_table()
    // {
    //     $table_name = $this->input->post('table_name');
    //     $fields = $this->input->post('fields');  // array of arrays ['name' => ..., 'type' => ..., 'extra' => ...]
    //     // $tipe = $this->input->post('tipe');
    //     if (empty($table_name)) {
    //         echo "Nama tabel wajib diisi!";
    //         return;
    //     }

    //     if (empty($fields) || !is_array($fields)) {
    //         echo "Fields wajib diisi!";
    //         return;
    //     }

    //     $fields_sql = [];
    //     foreach ($fields as $field) {
    //         if (empty($field['name']) || empty($field['type'])) continue;

    //         // Escape field name (simple)
    //         $name = $this->db->escape_str($field['name']);
    //         $type = strtoupper($this->db->escape_str($field['type']));
    //         $extra = isset($field['extra']) ? strtoupper($this->db->escape_str($field['extra'])) : '';

    //         // Buat string field, contoh: `id` INT(11) AUTO_INCREMENT PRIMARY KEY
    //         $field_line = "`$name` $type $extra";
    //         $fields_sql[] = $field_line;
    //     }

    //     if (count($fields_sql) == 0) {
    //         echo json_encode(array("status" => false, 'pesan' => "Tidak ada field yang valid!"));
    //         return;
    //     }

    //     $sql = "CREATE TABLE IF NOT EXISTS `" . $this->db->escape_str($table_name) . "` (";
    //     $sql .= implode(", ", $fields_sql);
    //     $sql .= ") ENGINE=InnoDB DEFAULT CHARSET=utf8";

    //     if ($this->db->query($sql)) {
    //         echo json_encode(array("status" => TRUE, 'pesan' => "Tabel '$table_name' berhasil dibuat!"));
    //     } else {
    //         echo json_encode(array("status" => false, 'pesan' =>  "Gagal membuat tabel. Periksa kembali data yang dimasukkan."));
    //     }
    // }
    public function create_table()
    {
        // Validasi CSRF
        // if ($this->input->method() !== 'post' || !$this->check_csrf_token()) {
        //     return $this->output
        //         ->set_content_type('application/json')
        //         ->set_output(json_encode([
        //             'status' => false,
        //             'pesan' => 'CSRF token tidak valid!',
        //             'csrf_token' => $this->security->get_csrf_hash()
        //         ]));
        // }

        $table_name = strtolower(trim($this->input->post('table_name', TRUE)));
        $fields = $this->input->post('fields');
        $generate = $this->input->post('generate', TRUE);

        if (!$table_name || empty($fields)) {
            echo json_encode([
                'status' => false,
                'pesan' => 'Nama tabel dan field tidak boleh kosong!',
                // 'csrf_token' => $this->security->get_csrf_hash()
            ]);
            return;
        }



        // Bersihkan nama tabel agar aman
        $table_name = preg_replace('/[^a-z0-9_]/', '', $table_name);

        // Cek jika tabel sudah ada
        if ($this->db->table_exists($table_name)) {
            echo json_encode([
                'status' => false,
                'pesan' => "Tabel '$table_name' sudah ada.",
                // 'csrf_token' => $this->security->get_csrf_hash()
            ]);
            return;
        }

        $field_definitions = [];
        $primary_key = null;

        foreach ($fields as $field) {
            $name = preg_replace('/[^a-zA-Z0-9_]/', '', $field['name']);
            $type = $field['type'];
            $extra_input = isset($field['extra']) ? $field['extra'] : [];
            if (!is_array($extra_input)) {
                $extra_input = [$extra_input]; // jika hanya satu, ubah jadi array
            }

            $extra = '';
            if (in_array('PRIMARY KEY', $extra_input)) {
                $primary_key = $name;
            }
            if (in_array('AUTO_INCREMENT', $extra_input)) {
                $extra = 'AUTO_INCREMENT';
            }



            $field_definitions[$name] = [
                'type' => $type,
                'null' => FALSE,
                'auto_increment' => $extra === 'AUTO_INCREMENT'
            ];
        }
        // die($primary_key);
        // Buat tabel
        try {
            $this->dbforge->add_field($field_definitions);

            if ($primary_key) {
                $this->dbforge->add_key($primary_key, TRUE);
            }

            $this->dbforge->create_table($table_name, TRUE);
            if ($generate == '1') {
                // Generate model, controller, dan view
                $this->generate($table_name, $fields);
            }
            // Balikan JSON sukses
            echo json_encode([
                'status' => true,
                'pesan' => "Tabel '$table_name' berhasil dibuat.",
                // 'csrf_token' => $this->security->get_csrf_hash()
            ]);
        } catch (Exception $e) {
            echo json_encode([
                'status' => false,
                'pesan' => 'Gagal membuat tabel: ' . $e->getMessage(),
                // 'csrf_token' => $this->security->get_csrf_hash()
            ]);
        }
    }

    public function generate($table, $fields)
    {
        $model = 'Mod_' . strtolower($table);
        $controller = ucfirst($table);
        $viewPath = APPPATH . "views/" . strtolower($table);
        if (!is_dir($viewPath)) mkdir($viewPath, 0755, true);
        $view = strtolower($table);

        // Simulasi generate file (buatkan file dengan isi contoh)
        file_put_contents(APPPATH . "models/$model.php", $this->model_generator($table, $fields));
        file_put_contents(APPPATH . "controllers/$controller.php", $this->controller_generator($table, $fields));
        file_put_contents(APPPATH .  "views/$view/$view.php", $this->view_generate($table, $fields));
        file_put_contents(APPPATH .  "views/$view/form_input.php", $this->form_generate($table, $fields));
    }

    function controller_generator($table, $fields)
    {
        $ClassName = ucfirst($table);
        $ModelName = "Mod_" . strtolower($table);
        $viewPath = "$table/$table"; // Bisa disesuaikan jika view ada di folder lain

        $code = "<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class $ClassName extends MY_Controller
{
    function __construct()
    {
        parent::__construct();
        \$this->load->model('$ModelName');
    }

    public function index()
    {
        \$link = '{$table}';
        \$level = \$this->session->userdata('id_level');

        \$jml = \$this->Mod_dashboard->get_akses_menu(\$link,\$level)->num_rows();
        if (\$jml > 0) {
            \$data['akses_menu'] = \$this->Mod_dashboard->get_akses_menu(\$link,\$level)->row();
            \$akses = \$data['akses_menu']->view;
        } else {
            \$data['akses_menu'] = \$this->Mod_dashboard->get_akses_submenu(\$link,\$level)->row();
            \$akses = \$data['akses_menu']->view;
        }

        if (\$akses == 'Y') {
            \$this->template->load('layoutbackend', '$viewPath', \$data);
        } else {
            \$data['page'] = \$link;
            \$this->template->load('layoutbackend','admin/akses_ditolak', \$data);
        }
    }

    public function ajax_list()
    {
        ini_set('memory_limit','512M');
        set_time_limit(3600);
        \$list = \$this->{$ModelName}->get_datatables();
        \$data = array();
        \$no = \$_POST['start'];
        foreach (\$list as \$row) {
            \$no++;
            \$sub_array = array();";
        foreach ($fields as $field) {
            if ($field['name'] != 'id') {
                $code .= "\$sub_array[] = \$row->" . $field['name'] . ";\n";
            }
        }
        $code .= "
        \$sub_array[] = \"<a class=\\\"btn btn-xs btn-outline-primary edit\\\" href=\\\"javascript:void(0)\\\" title=\\\"Edit\\\" onclick=\\\"edit('\$row->id')\\\"><i class=\\\"fas fa-edit\\\"></i></a>
                             <a class=\\\"btn btn-xs btn-outline-danger delete\\\" href=\\\"javascript:void(0)\\\" title=\\\"Delete\\\" onclick=\\\"hapus('\$row->id')\\\"><i class=\\\"fas fa-trash\\\"></i></a>\";
            \$data[] = \$sub_array;
        }

        \$output = array(
            \"draw\" => \$_POST['draw'],
            \"recordsTotal\" => \$this->{$ModelName}->count_all(),
            \"recordsFiltered\" => \$this->{$ModelName}->count_filtered(),
            \"data\" => \$data,
        );
        echo json_encode(\$output);
    }

     public function form_input()
    {
        \$this->load->view('{$table}/form_input');
    }
    public function insert()
    {
        \$save = array(";
        foreach ($fields as $field) {
            if ($field['name'] != 'id') {
                $code .= "'" . $field['name'] . "' => \$this->input->post('" . $field['name'] . "'),\n";
            }
        }
        $code .= "
        );
        \$this->{$ModelName}->insert('{$table}', \$save);
        echo json_encode(array(\"status\" => TRUE));
    }

    public function update()
    {
        \$id = \$this->input->post('id');
        \$save = array(";
        foreach ($fields as $field) {
            if ($field['name'] != 'id') {
                $code .= "'" . $field['name'] . "' => \$this->input->post('" . $field['name'] . "'),\n";
            }
        }
        $code .= "
        );
        \$this->{$ModelName}->update(\$id, \$save);
        echo json_encode(array(\"status\" => TRUE));
    }

    public function edit(\$id)
    {
        \$data = \$this->{$ModelName}->get(\$id);
        echo json_encode(\$data);
    }

    public function delete()
    {
        \$id = \$this->input->post('id');
        \$this->{$ModelName}->delete(\$id, '$table');
        echo json_encode(array(\"status\" => TRUE));
    }

    private function _validate()
    {
        \$data = array();
        \$data['error_string'] = array();
        \$data['inputerror'] = array();
        \$data['status'] = TRUE;";
        foreach ($fields as $field) {
            if ($field['name'] != 'id') {
                $code .= "

        if(\$this->input->post('" . $field['name'] . "') == '')
        {
            \$data['inputerror'][] = '" . $field['name'] . "';
            \$data['error_string'][] = '" . ucfirst($field['name']) . " Tidak Boleh Kosong';
            \$data['status'] = FALSE;
        }";
            }
        }
        $code .= "
        if(\$data['status'] === FALSE)
        {
            echo json_encode(\$data);
            exit();
        }
    }
}
";
        return $code;
    }


    function model_generator($table, $fields)
    {
        $model_name = "Mod_" . strtolower($table);
        $primary_key = "id"; // Anda bisa ubah ke field primary key tabel jika berbeda
        $formattedFields = array_map(function ($field) {
            return   $field['name'];
        }, $fields);
        $code = "<?php
defined('BASEPATH') or exit('No direct script access allowed');

class " . ucfirst($model_name) . " extends CI_Model
{
    var \$table = '$table';
    var \$column_search = array(";
        $fields = implode("', '", $formattedFields);
        $code .= "'$fields'";
        $code .= ");
    var \$column_order = array(";
        $fields = implode("', '", $formattedFields);
        $code .= "'$fields'";
        $code .= ");
    var \$order = array('$primary_key' => 'desc');

    function __construct()
    {
        parent::__construct();
        \$this->load->database();
    }

    private function _get_datatables_query()
    {
        \$this->db->from(\$this->table);
        \$i = 0;

        foreach (\$this->column_search as \$item)
        {
            if (\$_POST['search']['value'])
            {
                if (\$i === 0)
                {
                    \$this->db->group_start();
                    \$this->db->like(\$item, \$_POST['search']['value']);
                }
                else
                {
                    \$this->db->or_like(\$item, \$_POST['search']['value']);
                }

                if (count(\$this->column_search) - 1 == \$i)
                    \$this->db->group_end();
            }
            \$i++;
        }

        if (isset(\$_POST['order']))
        {
            \$this->db->order_by(\$this->column_order[\$_POST['order']['0']['column']], \$_POST['order']['0']['dir']);
        }
        else if (isset(\$this->order))
        {
            \$order = \$this->order;
            \$this->db->order_by(key(\$order), \$order[key(\$order)]);
        }
    }

    function get_datatables()
    {
        \$this->_get_datatables_query();
        if (\$_POST['length'] != -1)
            \$this->db->limit(\$_POST['length'], \$_POST['start']);
        \$query = \$this->db->get();
        return \$query->result();
    }

    function count_filtered()
    {
        \$this->_get_datatables_query();
        \$query = \$this->db->get();
        return \$query->num_rows();
    }

    function count_all()
    {
        \$this->db->from(\$this->table);
        return \$this->db->count_all_results();
    }

    function insert(\$table,\$data)
    {
        return \$this->db->insert(\$table, \$data);
    }

    function update(\$id, \$data)
    {
        \$this->db->where('$primary_key', \$id);
        \$this->db->update(\$this->table, \$data);
    }

    function get(\$id)
    {
        \$this->db->where('$primary_key', \$id);
        return \$this->db->get(\$this->table)->row();
    }

    function delete(\$id)
    {
        \$this->db->where('$primary_key', \$id);
        return \$this->db->delete(\$this->table);
    }
}
";
        return $code;
    }

    public function view_generate($table, $fields)
    {

        $data['table'] = $table;
        $data['fields'] = $fields;
        $html = $this->load->view('templates/tmp_view', $data, TRUE);

        return $html;
    }
    public function form_generate($table, $fields)
    {

        $data['table'] = $table;
        $data['fields'] = $fields;
        $html = $this->load->view('templates/tmp_form', $data, TRUE);

        return $html;
    }
}
