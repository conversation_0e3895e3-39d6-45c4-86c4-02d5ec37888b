<!-- Modern Minimalist CSS -->
<style>
:root {
    --primary-color: #6366f1;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-900: #111827;
}

.modern-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    border: 1px solid var(--gray-200);
    transition: all 0.2s ease;
}

.modern-card:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.stat-card {
    padding: 1.5rem;
    text-align: center;
    border-radius: 12px;
    background: white;
    border: 1px solid var(--gray-200);
    transition: all 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 12px;
    font-size: 20px;
    color: white;
}

.stat-icon.primary { background: linear-gradient(135deg, var(--primary-color), #8b5cf6); }
.stat-icon.success { background: linear-gradient(135deg, var(--success-color), #059669); }
.stat-icon.warning { background: linear-gradient(135deg, var(--warning-color), #d97706); }
.stat-icon.danger { background: linear-gradient(135deg, var(--danger-color), #dc2626); }

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 4px;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--gray-600);
    font-weight: 500;
}

.modern-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.badge-draft { background: var(--gray-100); color: var(--gray-600); }
.badge-pending { background: #fef3c7; color: #92400e; }
.badge-confirmed { background: #dbeafe; color: #1e40af; }
.badge-processing { background: #e0e7ff; color: var(--primary-color); }
.badge-shipped { background: #f3e8ff; color: #7c3aed; }
.badge-delivered { background: #d1fae5; color: #065f46; }
.badge-completed { background: #d1fae5; color: #065f46; }
.badge-cancelled { background: #fee2e2; color: #991b1b; }

.badge-low { background: #d1fae5; color: #065f46; }
.badge-normal { background: #dbeafe; color: #1e40af; }
.badge-high { background: #fef3c7; color: #92400e; }
.badge-urgent { background: #fee2e2; color: #991b1b; }

.badge-unpaid { background: #fee2e2; color: #991b1b; }
.badge-partial { background: #fef3c7; color: #92400e; }
.badge-paid { background: #d1fae5; color: #065f46; }
.badge-refunded { background: var(--gray-100); color: var(--gray-600); }

.compact-table {
    font-size: 0.875rem;
}

.compact-table th {
    background: var(--gray-50);
    border: none;
    padding: 0.75rem 1rem;
    font-weight: 600;
    color: var(--gray-700);
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
}

.compact-table td {
    padding: 1rem;
    border: none;
    border-bottom: 1px solid var(--gray-200);
    vertical-align: middle;
}

.compact-table tbody tr:hover {
    background: var(--gray-50);
}

.btn-modern {
    border-radius: 8px;
    font-weight: 500;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    border: none;
}

.btn-modern:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.section-divider {
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--gray-200), transparent);
    margin: 2rem 0;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--gray-100);
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-label {
    font-weight: 500;
    color: var(--gray-600);
    font-size: 0.875rem;
}

.detail-value {
    font-weight: 600;
    color: var(--gray-900);
}

/* Horizontal Order Info Bar */
.order-info-bar {
    background: white;
    border-radius: 12px;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    border: 1px solid var(--gray-200);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.order-info-bar .row {
    align-items: stretch;
}

.order-info-bar .col-lg-3,
.order-info-bar .col-md-6,
.order-info-bar .col-sm-6 {
    display: flex;
}

.info-item {
    display: flex;
    align-items: flex-start;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    background: var(--gray-50);
    transition: all 0.2s ease;
    min-height: 80px;
    width: 100%;
    height: 100%;
}

.info-item:hover {
    background: var(--gray-100);
    transform: translateY(-1px);
}

.info-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    margin-top: 2px;
    font-size: 16px;
    color: white;
    flex-shrink: 0;
    position: relative;
    text-align: center;
    line-height: 1;
}

.info-icon i {
    display: block;
    width: 100%;
    text-align: center;
    vertical-align: middle;
}

.info-icon.primary { background: linear-gradient(135deg, var(--primary-color), #8b5cf6); }
.info-icon.success { background: linear-gradient(135deg, var(--success-color), #059669); }
.info-icon.warning { background: linear-gradient(135deg, var(--warning-color), #d97706); }
.info-icon.danger { background: linear-gradient(135deg, var(--danger-color), #dc2626); }

.info-content {
    flex: 1;
    min-width: 0;
    padding-top: 2px;
}

.info-title {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--gray-600);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 2px;
}

.info-value {
    font-size: 1rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 4px;
    word-break: break-word;
}

.info-subtitle {
    font-size: 0.75rem;
    color: var(--gray-500);
    margin-bottom: 4px;
}

.info-badge {
    display: inline-block;
}

/* Additional Information Section */
.additional-info-bar {
    background: white;
    border-radius: 12px;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    border: 1px solid var(--gray-200);
    margin-bottom: 1.5rem;
    overflow: hidden;
}

.additional-info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    background: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
    cursor: pointer;
    transition: all 0.2s ease;
}

.additional-info-header:hover {
    background: var(--gray-100);
}

.additional-info-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--gray-700);
    margin: 0;
    display: flex;
    align-items: center;
}

.additional-info-title i {
    margin-right: 8px;
    color: var(--primary-color);
}

.toggle-icon {
    transition: transform 0.2s ease;
    color: var(--gray-500);
}

.toggle-icon.rotated {
    transform: rotate(180deg);
}

.additional-info-content {
    padding: 1.5rem;
}

.additional-info-item {
    display: flex;
    align-items: flex-start;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    background: var(--gray-50);
    margin-bottom: 0.75rem;
    transition: all 0.2s ease;
}

.additional-info-item:last-child {
    margin-bottom: 0;
}

.additional-info-item:hover {
    background: var(--gray-100);
}

.additional-info-icon {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    font-size: 14px;
    color: white;
    flex-shrink: 0;
}

.additional-info-icon.info { background: linear-gradient(135deg, #3b82f6, #1d4ed8); }
.additional-info-icon.calendar { background: linear-gradient(135deg, #8b5cf6, #7c3aed); }
.additional-info-icon.note { background: linear-gradient(135deg, #f59e0b, #d97706); }

.additional-info-details {
    flex: 1;
    min-width: 0;
}

.additional-info-label {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--gray-600);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 4px;
}

.additional-info-value {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--gray-900);
    line-height: 1.4;
    word-break: break-word;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .order-info-bar {
        padding: 1rem;
    }

    .info-item {
        padding: 0.5rem 0.75rem;
        margin-bottom: 0.5rem;
    }

    .info-icon {
        width: 32px;
        height: 32px;
        font-size: 14px;
        margin-right: 8px;
    }

    .info-value {
        font-size: 0.875rem;
    }

    .additional-info-content {
        padding: 1rem;
    }

    .additional-info-item {
        padding: 0.5rem 0.75rem;
    }

    .additional-info-icon {
        width: 28px;
        height: 28px;
        font-size: 12px;
        margin-right: 8px;
    }
}

/* Order Items Section */
.order-items-bar {
    background: white;
    border-radius: 12px;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    border: 1px solid var(--gray-200);
    margin-bottom: 1.5rem;
    overflow: hidden;
}

.order-items-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    background: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
}

.order-items-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--gray-700);
    margin: 0;
    display: flex;
    align-items: center;
}

.order-items-title i {
    margin-right: 8px;
    color: var(--primary-color);
}

.btn-add-item {
    background: linear-gradient(135deg, var(--primary-color), #8b5cf6);
    border: none;
    border-radius: 8px;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: white;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
}

.btn-add-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
    color: white;
}

.btn-add-item i {
    margin-right: 6px;
}

/* Enhanced Table Styling */
.order-items-table {
    margin: 0;
}

.order-items-table thead th {
    background: var(--gray-50);
    border: none;
    padding: 1rem;
    font-weight: 600;
    color: var(--gray-700);
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
    border-bottom: 2px solid var(--gray-200);
}

.order-items-table tbody td {
    padding: 0.6rem 1rem;
    border: none;
    border-bottom: 1px solid var(--gray-100);
    vertical-align: middle;
    font-size: 0.875rem;
}

.order-items-table tbody tr {
    transition: all 0.2s ease;
}

.order-items-table tbody tr:hover {
    background: var(--gray-50);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.order-items-table tbody tr:last-child td {
    border-bottom: none;
}

/* Action Buttons */
.action-btn {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    border: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin: 0 2px;
    transition: all 0.2s ease;
    font-size: 0.75rem;
}

.action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.action-btn.edit {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
}

.action-btn.delete {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
}

/* Summary Footer Enhancement */
.order-summary-footer {
    padding: 1.5rem;
    background: linear-gradient(135deg, var(--gray-50), #f8fafc);
    border-top: 1px solid var(--gray-200);
}

.summary-stat-item {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem;
    border-radius: 8px;
    background: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.summary-stat-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.summary-stat-icon {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
    font-size: 14px;
    color: white;
    flex-shrink: 0;
}

.summary-stat-content {
    text-align: left;
}

.summary-stat-value {
    font-size: 1rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 2px;
}

.summary-stat-label {
    font-size: 0.75rem;
    color: var(--gray-600);
    font-weight: 500;
}

.final-total-card {
    background: white;
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border-left: 4px solid var(--primary-color);
}

.final-total-label {
    font-size: 0.75rem;
    color: var(--gray-600);
    font-weight: 500;
    margin-bottom: 4px;
}

.final-total-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 2px;
}

.final-total-note {
    font-size: 0.75rem;
    color: var(--gray-500);
}
</style>

<!-- Main content -->
<section class="content modern-container">
    <div class="container-fluid">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2 class="mb-1 font-weight-bold text-gray-900">Order <?= $order->order_no ?></h2>
                <p class="text-muted mb-0"><?= date('d M Y, H:i', strtotime($order->order_date)) ?></p>
            </div>
            <a href="<?= base_url('orders') ?>" class="btn btn-outline-secondary btn-modern">
                <i class="fas fa-arrow-left mr-2"></i>Back
            </a>
        </div>

        <!-- Horizontal Order Info Bar -->
        <div class="order-info-bar">
            <div class="row g-3">
                <!-- Order Number -->
                <div class="col-lg-3 col-md-6 col-sm-6">
                    <div class="info-item">
                        <div class="info-icon primary">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-title">Order Number</div>
                            <div class="info-value"><?= $order->order_no ?></div>
                            <div class="info-subtitle"><?= date('d M Y', strtotime($order->order_date)) ?></div>
                            <div class="info-badge">
                                <span class="modern-badge badge-<?php
                                    switch($order->order_status) {
                                        case 'draft': echo 'draft'; break;
                                        case 'pending': echo 'pending'; break;
                                        case 'confirmed': echo 'confirmed'; break;
                                        case 'processing': echo 'processing'; break;
                                        case 'delivered': echo 'delivered'; break;
                                        case 'completed': echo 'completed'; break;
                                        case 'cancelled': echo 'cancelled'; break;
                                        default: echo 'draft';
                                    }
                                ?>"><?= ucfirst($order->order_status) ?></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Customer -->
                <div class="col-lg-3 col-md-6 col-sm-6">
                    <div class="info-item">
                        <div class="info-icon success">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-title">Customer</div>
                            <div class="info-value"><?= $order->customer_name ?: 'Walk-in Customer' ?></div>
                            <div class="info-subtitle"><?= $order->customer_phone ?: 'No phone' ?></div>
                        </div>
                    </div>
                </div>

                <!-- Priority -->
                <div class="col-lg-3 col-md-6 col-sm-6">
                    <div class="info-item">
                        <div class="info-icon warning">
                            <i class="fas fa-flag"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-title">Priority</div>
                            <div class="info-value">
                                <span class="modern-badge badge-<?php
                                    switch($order->priority) {
                                        case 'low': echo 'low'; break;
                                        case 'normal': echo 'normal'; break;
                                        case 'high': echo 'high'; break;
                                        case 'urgent': echo 'urgent'; break;
                                        default: echo 'normal';
                                    }
                                ?>"><?= ucfirst($order->priority) ?></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Total Amount -->
                <div class="col-lg-3 col-md-6 col-sm-6">
                    <div class="info-item">
                        <div class="info-icon danger">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-title">Total Amount</div>
                            <div class="info-value">Rp <?= number_format($order->total_amount, 0, ',', '.') ?></div>
                            <div class="info-badge">
                                <span class="modern-badge badge-<?php
                                    switch($order->payment_status) {
                                        case 'unpaid': echo 'unpaid'; break;
                                        case 'partial': echo 'partial'; break;
                                        case 'paid': echo 'paid'; break;
                                        default: echo 'unpaid';
                                    }
                                ?>"><?= ucfirst($order->payment_status) ?></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Additional Information -->
        <?php if ($order->customer_email || $order->customer_address || $order->due_date || $order->delivery_date || $order->order_notes): ?>
        <div class="additional-info-bar">
            <div class="additional-info-header" data-toggle="collapse" data-target="#additionalInfo" aria-expanded="false">
                <h6 class="additional-info-title">
                    <i class="fas fa-info-circle"></i>
                    Additional Information
                </h6>
                <i class="fas fa-chevron-down toggle-icon"></i>
            </div>
            <div class="collapse" id="additionalInfo">
                <div class="additional-info-content">
                    <?php if ($order->customer_email || $order->customer_address): ?>
                    <div class="additional-info-item">
                        <div class="additional-info-icon info">
                            <i class="fas fa-user-circle"></i>
                        </div>
                        <div class="additional-info-details">
                            <div class="additional-info-label">Customer Details</div>
                            <div class="additional-info-value">
                                <?php if ($order->customer_email): ?>
                                    <strong>Email:</strong> <?= $order->customer_email ?><br>
                                <?php endif; ?>
                                <?php if ($order->customer_address): ?>
                                    <strong>Address:</strong> <?= $order->customer_address ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <?php if ($order->due_date || $order->delivery_date): ?>
                    <div class="additional-info-item">
                        <div class="additional-info-icon calendar">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <div class="additional-info-details">
                            <div class="additional-info-label">Important Dates</div>
                            <div class="additional-info-value">
                                <?php if ($order->due_date): ?>
                                    <strong>Due Date:</strong> <?= date('d M Y', strtotime($order->due_date)) ?><br>
                                <?php endif; ?>
                                <?php if ($order->delivery_date): ?>
                                    <strong>Delivery Date:</strong> <?= date('d M Y', strtotime($order->delivery_date)) ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <?php if ($order->order_notes): ?>
                    <div class="additional-info-item">
                        <div class="additional-info-icon note">
                            <i class="fas fa-sticky-note"></i>
                        </div>
                        <div class="additional-info-details">
                            <div class="additional-info-label">Order Notes</div>
                            <div class="additional-info-value"><?= nl2br($order->order_notes) ?></div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Order Items -->
        <div class="order-items-bar" style="padding: 15px; margin-bottom: 20px;">
            <div class="order-items-header">
                <h6 class="order-items-title">
                    <i class="fas fa-shopping-cart"></i>
                    Order Items
                </h6>
                <button type="button" class="btn-add-item" onclick="addDetail()">
                    <i class="fas fa-plus"></i>Add Item
                </button>
            </div>

            <div class="table-responsive">
                <table id="tbl_order_detail" class="order-items-table table mb-0">
                    <thead>
                        <tr>
                            <th>Item</th>
                            <th>Unit</th>
                            <th class="text-center">Qty</th>
                            <th class="text-right">Price</th>
                            <th class="text-right">Subtotal</th>
                            <th>Notes</th>
                            <th class="text-center">Actions</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>

            <!-- Enhanced Summary Footer -->
            <div class="order-summary-footer">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <div class="row">
                            <div class="col-4">
                                <div class="summary-stat-item">
                                    <div class="summary-stat-icon primary" style="background: linear-gradient(135deg, var(--primary-color), #8b5cf6);">
                                        <i class="fas fa-boxes"></i>
                                    </div>
                                    <div class="summary-stat-content">
                                        <div class="summary-stat-value" id="total-items">0</div>
                                        <div class="summary-stat-label">Items</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="summary-stat-item">
                                    <div class="summary-stat-icon success" style="background: linear-gradient(135deg, var(--success-color), #059669);">
                                        <i class="fas fa-sort-numeric-up"></i>
                                    </div>
                                    <div class="summary-stat-content">
                                        <div class="summary-stat-value" id="total-qty">0</div>
                                        <div class="summary-stat-label">Quantity</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="summary-stat-item">
                                    <div class="summary-stat-icon warning" style="background: linear-gradient(135deg, var(--warning-color), #d97706);">
                                        <i class="fas fa-calculator"></i>
                                    </div>
                                    <div class="summary-stat-content">
                                        <div class="summary-stat-value" id="grand-total">Rp 0</div>
                                        <div class="summary-stat-label">Subtotal</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="final-total-card">
                            <div class="final-total-label" id="final-total-label">Final Total</div>
                            <div class="final-total-value">
                                Rp <?= number_format($order->total_amount, 0, ',', '.') ?>
                            </div>
                            <div class="final-total-note">Inc. tax & shipping</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>



<script type="text/javascript">
    var save_method_detail; //for save method string
    var table_detail;
    var order_id = <?= $order_id ?>;

    $(document).ready(function() {
        //datatables for order detail
        table_detail = $("#tbl_order_detail").DataTable({
            "responsive": true,
            "autoWidth": false,
            "language": {
                "sEmptyTable": "Belum Ada Item Order"
            },
            "processing": true,
            "serverSide": true,
            "order": [],

            "ajax": {
                "url": "<?= base_url('orders/ajax_list_detail') ?>",
                "type": "POST",
                "data": function(d) {
                    d.order_id = order_id;
                }
            },
            "drawCallback": function(settings) {
                updateOrderSummary();
                calculateTotal();
            }
        });

        // Additional Information toggle animation
        $('#additionalInfo').on('show.bs.collapse', function () {
            $('.toggle-icon').addClass('rotated');
        });

        $('#additionalInfo').on('hide.bs.collapse', function () {
            $('.toggle-icon').removeClass('rotated');
        });

        //set input/textarea/select event when change value, remove class error and remove text help block
        $("input").change(function() {
            $(this).parent().parent().removeClass('has-error');
            $(this).next().empty();
            $(this).removeClass('is-invalid');
        });
        $("textarea").change(function() {
            $(this).parent().parent().removeClass('has-error');
            $(this).next().empty();
            $(this).removeClass('is-invalid');
        });
        $("select").change(function() {
            $(this).parent().parent().removeClass('has-error');
            $(this).next().empty();
            $(this).removeClass('is-invalid');
        });
    });

    function reload_table_detail() {
        table_detail.ajax.reload(null, false);
    }

    function updateOrderSummary() {
        $.ajax({
            url: "<?= base_url('orders/get_order_summary') ?>",
            type: "POST",
            data: {order_id: order_id},
            dataType: "JSON",
            success: function(data) {
                $('#total-items').text(data.total_items);
                $('#total-qty').text(parseFloat(data.total_qty).toFixed(2));
                $('#grand-total').text('Rp ' + number_format(data.grand_total, 0, ',', '.'));
            }
        });
    }

    function calculateTotal() {
        $.ajax({
            url: "<?= base_url('orders/calculate_total') ?>",
            type: "POST",
            data: {order_id: order_id},
            dataType: "JSON",
            success: function(data) {
                // Update financial summary
                $('.final-total-value').text('Rp ' + number_format(data.total_amount, 0, ',', '.'));
            }
        });
    }

    function number_format(number, decimals, dec_point, thousands_sep) {
        number = (number + '').replace(/[^0-9+\-Ee.]/g, '');
        var n = !isFinite(+number) ? 0 : +number,
            prec = !isFinite(+decimals) ? 0 : Math.abs(decimals),
            sep = (typeof thousands_sep === 'undefined') ? ',' : thousands_sep,
            dec = (typeof dec_point === 'undefined') ? '.' : dec_point,
            s = '',
            toFixedFix = function(n, prec) {
                var k = Math.pow(10, prec);
                return '' + Math.round(n * k) / k;
            };
        s = (prec ? toFixedFix(n, prec) : '' + Math.round(n)).split('.');
        if (s[0].length > 3) {
            s[0] = s[0].replace(/\B(?=(?:\d{3})+(?!\d))/g, sep);
        }
        if ((s[1] || '').length < prec) {
            s[1] = s[1] || '';
            s[1] += new Array(prec - s[1].length + 1).join('0');
        }
        return s.join(dec);
    }

    const Toast = Swal.mixin({
        toast: true,
        position: 'top-end',
        showConfirmButton: false,
        timer: 3000
    });

    //delete detail
    function hapusDetail(id) {
        Swal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, delete it!'
        }).then((result) => {
            if (result.value) {
                $.ajax({
                    url: "<?= base_url('orders/delete_detail') ?>",
                    type: "POST",
                    data: "id=" + id,
                    cache: false,
                    dataType: 'json',
                    success: function(respone) {
                        if (respone.status == true) {
                            reload_table_detail();
                            Swal.fire(
                                'Deleted!',
                                'Item has been deleted.',
                                'success'
                            );
                        } else {
                            Toast.fire({
                                icon: 'error',
                                title: 'Delete Error!!.'
                            });
                        }
                    }
                });
            }
        })
    }

    function addDetail() {
        save_method_detail = 'add';
        $('.form-group').removeClass('has-error');
        $('.help-block').empty();
        $.ajax({
            url: "<?= base_url('orders/form_input_detail') ?>",
            type: "GET",
            dataType: "html",
            success: function(data) {
                $('#modal-body-detail').html(data);
                $('#modal_form_detail').modal('show');
                $('.modal-title-detail').text('Add Item Order');

                // Reset form setelah modal terbuka
                setTimeout(function() {
                    $('#form_detail')[0].reset();
                    $('#id_barang').val('');
                    $('#nama_barang').focus();
                }, 500);
            },
            error: function(jqXHR, textStatus, errorThrown) {
                alert('Error get data from ajax');
            }
        });
        $('#modal_form_detail').modal({
            backdrop: 'static',
            keyboard: false
        });
        $('.modal-title-detail').text('Add Item Order');
    }

    function editDetail(id) {
        save_method_detail = 'update';
        $('.form-group').removeClass('has-error');
        $('.help-block').empty();
        $.ajax({
            url: "<?= base_url('orders/form_input_detail') ?>",
            type: "GET",
            dataType: "html",
            success: function(data) {
                $('#modal-body-detail').html(data);
                $('#modal_form_detail').modal('show');
                //Ajax Load data from ajax
                $.ajax({
                    url: "<?= base_url('orders/edit_detail') ?>/" + id,
                    type: "GET",
                    dataType: "JSON",
                    success: function(data) {
                        $('[name="id"]').val(data.id);
                        $('[name="id_barang"]').val(data.id_barang);
                        $('[name="nama_barang"]').val(data.nama_barang);
                        $('[name="qty"]').val(data.qty);
                        $('[name="harga"]').val(data.harga);
                        $('[name="keterangan"]').val(data.keterangan);

                        $('#modal_form_detail').modal('show');
                        $('.modal-title-detail').text('Edit Item Order');
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        alert('Error get data from ajax');
                    }
                });
            },
            error: function(jqXHR, textStatus, errorThrown) {
                alert('Error get data from ajax');
            }
        });
    }

    function saveDetail() {
        // Validasi client-side
        if ($('#id_barang').val() == '') {
            Swal.fire({
                title: 'Error!',
                text: 'Silakan pilih barang dari daftar autocomplete',
                icon: 'error',
                showConfirmButton: true,
            });
            return false;
        }

        if ($('#qty').val() == '' || $('#qty').val() <= 0) {
            Swal.fire({
                title: 'Error!',
                text: 'Quantity harus lebih dari 0',
                icon: 'error',
                showConfirmButton: true,
            });
            return false;
        }

        if ($('#harga').val() == '' || $('#harga').val() < 0) {
            Swal.fire({
                title: 'Error!',
                text: 'Harga tidak boleh kosong',
                icon: 'error',
                showConfirmButton: true,
            });
            return false;
        }

        $('#btnSaveDetail').text('saving...');
        $('#btnSaveDetail').attr('disabled', true);

        if (save_method_detail == 'add') {
            url = "<?= base_url('orders/insert_detail') ?>";
        } else {
            url = "<?= base_url('orders/update_detail') ?>";
        }

        var formdata = new FormData($('#form_detail')[0]);
        formdata.append('order_id', order_id);

        $.ajax({
            url: url,
            type: "POST",
            data: formdata,
            dataType: "JSON",
            cache: false,
            contentType: false,
            processData: false,
            success: function(data) {
                if (data.status) {
                    $('#modal_form_detail').modal('hide');
                    reload_table_detail();
                    Toast.fire({
                        icon: 'success',
                        title: 'Success!!.'
                    });
                } else {
                    Swal.fire({
                        title: 'Error!',
                        text: data.pesan || 'Terjadi kesalahan saat menyimpan data',
                        icon: 'error',
                        showConfirmButton: true,
                    });
                }
                $('#btnSaveDetail').text('save');
                $('#btnSaveDetail').attr('disabled', false);
            },
            error: function(jqXHR, textStatus, errorThrown) {
                console.log('Error details:', jqXHR.responseText);
                Swal.fire({
                    title: 'Error!',
                    text: 'Error adding / update data: ' + errorThrown,
                    icon: 'error',
                    showConfirmButton: true,
                });
                $('#btnSaveDetail').text('save');
                $('#btnSaveDetail').attr('disabled', false);
            }
        });
    }
</script>

<!-- Bootstrap modal for detail -->
<div class="modal fade" id="modal_form_detail" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title-detail"></h3>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="modal-body-detail">
            </div>
            <div class="modal-footer">
                <button type="button" id="btnSaveDetail" onclick="saveDetail()" class="btn btn-primary">Save</button>
                <button type="button" class="btn btn-danger" data-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>





