<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Mod_orders extends CI_Model
{
    var $table = 'orders';
    var $column_search = array('order_no', 'order_date', 'customer_name', 'customer_phone', 'order_status', 'priority', 'payment_status');
    var $column_order = array('order_no', 'order_date', 'customer_name', 'order_status', 'priority', 'payment_status', 'total_amount');
    var $order = array('id' => 'desc');

    function __construct()
    {
        parent::__construct();
        $this->load->database();
    }

    private function _get_datatables_query()
    {
        $this->db->from($this->table);
        $i = 0;

        foreach ($this->column_search as $item) {
            if ($_POST['search']['value']) {
                if ($i === 0) {
                    $this->db->group_start();
                    $this->db->like($item, $_POST['search']['value']);
                } else {
                    $this->db->or_like($item, $_POST['search']['value']);
                }

                if (count($this->column_search) - 1 == $i)
                    $this->db->group_end();
            }
            $i++;
        }

        if (isset($_POST['order'])) {
            $this->db->order_by($this->column_order[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
        } else if (isset($this->order)) {
            $order = $this->order;
            $this->db->order_by(key($order), $order[key($order)]);
        }
    }

    function get_datatables()
    {
        $this->_get_datatables_query();
        if ($_POST['length'] != -1)
            $this->db->limit($_POST['length'], $_POST['start']);
        $query = $this->db->get();
        return $query->result();
    }

    function count_filtered()
    {
        $this->_get_datatables_query();
        $query = $this->db->get();
        return $query->num_rows();
    }

    function count_all()
    {
        $this->db->from($this->table);
        return $this->db->count_all_results();
    }

    function insert($table, $data)
    {
        return $this->db->insert($table, $data);
    }

    function update($id, $data)
    {
        $this->db->where('id', $id);
        $this->db->update($this->table, $data);
    }

    function get($id)
    {
        $this->db->where('id', $id);
        return $this->db->get($this->table)->row();
    }

    function delete($id, $table)
    {
        $this->db->where('id', $id);
        $this->db->delete($table);
    }

    // Methods for order detail
    var $table_detail = 'order_detail';
    var $column_search_detail = array('b.nama', 'c.nama', 'a.qty', 'a.harga', 'a.keterangan');
    var $column_order_detail = array('b.nama', 'c.nama', 'a.qty', 'a.harga', 'a.keterangan');
    var $order_detail = array('a.id' => 'desc');

    private function _get_datatables_query_detail($order_id)
    {
        $this->db->select('a.*, b.nama as nama_barang, c.nama as nama_satuan');
        $this->db->from($this->table_detail . ' a');
        $this->db->join('barang b', 'a.id_barang = b.id', 'left');
        $this->db->join('satuan c', 'b.id_satuan = c.id', 'left');
        $this->db->where('a.order_id', $order_id);

        $i = 0;
        foreach ($this->column_search_detail as $item) {
            if ($_POST['search']['value']) {
                if ($i === 0) {
                    $this->db->group_start();
                    $this->db->like($item, $_POST['search']['value']);
                } else {
                    $this->db->or_like($item, $_POST['search']['value']);
                }

                if (count($this->column_search_detail) - 1 == $i)
                    $this->db->group_end();
            }
            $i++;
        }

        if (isset($_POST['order'])) {
            $this->db->order_by($this->column_order_detail[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
        } else if (isset($this->order_detail)) {
            $order = $this->order_detail;
            $this->db->order_by(key($order), $order[key($order)]);
        }
    }

    function get_detail_datatables($order_id)
    {
        $this->_get_datatables_query_detail($order_id);
        if ($_POST['length'] != -1)
            $this->db->limit($_POST['length'], $_POST['start']);
        $query = $this->db->get();
        return $query->result();
    }

    function count_filtered_detail($order_id)
    {
        $this->_get_datatables_query_detail($order_id);
        $query = $this->db->get();
        return $query->num_rows();
    }

    function count_all_detail($order_id)
    {
        $this->db->from($this->table_detail);
        $this->db->where('order_id', $order_id);
        return $this->db->count_all_results();
    }

    function get_detail($id)
    {
        $this->db->select('a.*, b.nama as nama_barang, c.nama as nama_satuan');
        $this->db->from($this->table_detail . ' a');
        $this->db->join('barang b', 'a.id_barang = b.id', 'left');
        $this->db->join('satuan c', 'b.id_satuan = c.id', 'left');
        $this->db->where('a.id', $id);
        return $this->db->get()->row();
    }

    function update_detail($id, $data)
    {
        $this->db->where('id', $id);
        $this->db->update($this->table_detail, $data);
    }

    function get_barang($params)
    {
        $this->db->select('a.*, b.nama as nama_satuan');
        $this->db->from('barang a');
        $this->db->join('satuan b', 'a.id_satuan = b.id', 'left');
        $this->db->like('a.nama', $params);
        $this->db->or_like('a.barcode', $params);
        $this->db->or_like('a.kdbarang', $params);
        $this->db->where('a.aktivasi', 'Ya');
        $this->db->limit(10);
        return $this->db->get()->result();
    }

    function get_order_summary($order_id)
    {
        $this->db->select('COUNT(*) as total_items, SUM(qty) as total_qty, SUM(qty * harga) as grand_total');
        $this->db->from($this->table_detail);
        $this->db->where('order_id', $order_id);
        $result = $this->db->get()->row();

        return array(
            'total_items' => $result->total_items ? $result->total_items : 0,
            'total_qty' => $result->total_qty ? $result->total_qty : 0,
            'grand_total' => $result->grand_total ? $result->grand_total : 0
        );
    }
}
