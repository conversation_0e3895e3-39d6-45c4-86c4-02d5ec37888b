SET foreign_key_checks = 0;
#
# TABLE STRUCTURE FOR: aplikasi
#

DROP TABLE IF EXISTS `aplikasi`;

CREATE TABLE `aplikasi` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `nama_owner` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `alamat` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci,
  `tlp` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `brand` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `title` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `nama_aplikasi` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `logo` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `copy_right` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `versi` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `tahun` year DEFAULT NULL,
  `email` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `nama_pengirim` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `password` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=latin1;

INSERT INTO `aplikasi` (`id`, `nama_owner`, `alamat`, `tlp`, `brand`, `title`, `nama_aplikasi`, `logo`, `copy_right`, `versi`, `tahun`, `email`, `nama_pengirim`, `password`) VALUES (1, 'PT. ABC', 'jalan raya', '085838333009', NULL, 'Toko Elektronik', 'Toko Elektronik', 'Logo.png', 'Copy Right ©', '1.0.0.2', '2025', '<EMAIL>', 'Aryo Coding', 'pfpinffqxutdjexq');


#
# TABLE STRUCTURE FOR: barang
#

DROP TABLE IF EXISTS `barang`;

CREATE TABLE `barang` (
  `id` int NOT NULL AUTO_INCREMENT,
  `barcode` varchar(15) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `kdbarang` varchar(15) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `nama` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `harga` decimal(10,0) DEFAULT NULL,
  `id_satuan` int DEFAULT NULL,
  `perundangan` int DEFAULT NULL,
  `berat` varchar(12) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `rak` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `aktivasi` enum('Ya','Tidak') CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT 'Ya',
  `user_input` int DEFAULT NULL,
  `stok` double NOT NULL DEFAULT '0',
  `batch` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `thn_pengadaan` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `no_ijin_edar` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `manufaktur_date` date DEFAULT NULL,
  `nosbbk` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `sumber_dana` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `ed` date DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `nama_barang` (`nama`),
  KEY `perundangan` (`perundangan`),
  KEY `kemasan` (`id_satuan`),
  CONSTRAINT `barang_ibfk_3` FOREIGN KEY (`id_satuan`) REFERENCES `satuan` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=latin1;

INSERT INTO `barang` (`id`, `barcode`, `kdbarang`, `nama`, `harga`, `id_satuan`, `perundangan`, `berat`, `rak`, `aktivasi`, `user_input`, `stok`, `batch`, `thn_pengadaan`, `no_ijin_edar`, `manufaktur_date`, `nosbbk`, `sumber_dana`, `ed`) VALUES (1, '2341', '2341', 'Barang 1', '8000', 1, 1, 'gr', 'R1', 'Ya', 1, '0', '', '', '', '0000-00-00', '', '', '0000-00-00');
INSERT INTO `barang` (`id`, `barcode`, `kdbarang`, `nama`, `harga`, `id_satuan`, `perundangan`, `berat`, `rak`, `aktivasi`, `user_input`, `stok`, `batch`, `thn_pengadaan`, `no_ijin_edar`, `manufaktur_date`, `nosbbk`, `sumber_dana`, `ed`) VALUES (4, '1234', '1234', 'Barang 2', '90000', 1, 1, '12333', 'R1', 'Ya', 1, '0', '', '', '', '0000-00-00', '', '', '0000-00-00');


#
# TABLE STRUCTURE FOR: gudang
#

DROP TABLE IF EXISTS `gudang`;

CREATE TABLE `gudang` (
  `id` int NOT NULL AUTO_INCREMENT,
  `nama` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb3;

INSERT INTO `gudang` (`id`, `nama`) VALUES (1, 'Gudang A');
INSERT INTO `gudang` (`id`, `nama`) VALUES (2, 'Gudang B');


#
# TABLE STRUCTURE FOR: keluar
#

DROP TABLE IF EXISTS `keluar`;

CREATE TABLE `keluar` (
  `id` int NOT NULL AUTO_INCREMENT,
  `id_pelanggan` int DEFAULT NULL,
  `tanggal` datetime DEFAULT NULL,
  `user_input` int DEFAULT NULL,
  `id_gudang` int DEFAULT NULL,
  `tgl_input` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `faktur` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `id_supir` int DEFAULT NULL,
  `ritke` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `id_pelanggan` (`id_pelanggan`),
  KEY `user_input` (`user_input`),
  KEY `id_gudang` (`id_gudang`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=latin1;

#
# TABLE STRUCTURE FOR: keluar_detail
#

DROP TABLE IF EXISTS `keluar_detail`;

CREATE TABLE `keluar_detail` (
  `id` int NOT NULL AUTO_INCREMENT,
  `id_keluar` int DEFAULT NULL,
  `id_barang` int DEFAULT NULL,
  `jumlah` double DEFAULT '0',
  `sisa` double NOT NULL DEFAULT '0',
  `harga` double NOT NULL DEFAULT '0',
  `harga_jual` double NOT NULL DEFAULT '0',
  `kemasan` int DEFAULT NULL,
  `ed` date DEFAULT NULL,
  `nobatch` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `id_user` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `id_barang` (`id_barang`),
  KEY `id_keluar` (`id_keluar`),
  KEY `id_user` (`id_user`),
  CONSTRAINT `keluar_detail_ibfk_1` FOREIGN KEY (`id_barang`) REFERENCES `barang` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `keluar_detail_ibfk_3` FOREIGN KEY (`id_user`) REFERENCES `tbl_user` (`id_user`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=latin1;

#
# TABLE STRUCTURE FOR: pelanggan
#

DROP TABLE IF EXISTS `pelanggan`;

CREATE TABLE `pelanggan` (
  `id` int NOT NULL AUTO_INCREMENT,
  `nama` varchar(255) NOT NULL,
  `no_telepon` varchar(255) NOT NULL,
  `alamat` text NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb3;

INSERT INTO `pelanggan` (`id`, `nama`, `no_telepon`, `alamat`) VALUES (1, 'Pelangan', '12321312', 'tester alamat');


#
# TABLE STRUCTURE FOR: penerimaan
#

DROP TABLE IF EXISTS `penerimaan`;

CREATE TABLE `penerimaan` (
  `id` int NOT NULL AUTO_INCREMENT,
  `faktur` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `tanggal` date DEFAULT NULL,
  `id_supplier` int DEFAULT NULL,
  `user_input` int DEFAULT NULL,
  `id_gudang` int DEFAULT NULL,
  `tgl_input` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `sumber` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '',
  PRIMARY KEY (`id`),
  KEY `id_gudang` (`id_gudang`),
  KEY `id_supplier` (`id_supplier`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=latin1;

#
# TABLE STRUCTURE FOR: penerimaan_detail
#

DROP TABLE IF EXISTS `penerimaan_detail`;

CREATE TABLE `penerimaan_detail` (
  `id` int NOT NULL AUTO_INCREMENT,
  `id_penerimaan` int DEFAULT NULL,
  `id_barang` int DEFAULT NULL,
  `harga` double DEFAULT NULL,
  `jumlah` double DEFAULT NULL,
  `id_satuan` int DEFAULT NULL,
  `nobatch` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `ed` date DEFAULT NULL,
  `id_user` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `id_penerimaan` (`id_penerimaan`),
  KEY `id_barang` (`id_barang`),
  KEY `kemasan` (`id_satuan`),
  CONSTRAINT `penerimaan_detail_ibfk_2` FOREIGN KEY (`id_barang`) REFERENCES `barang` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

#
# TABLE STRUCTURE FOR: satuan
#

DROP TABLE IF EXISTS `satuan`;

CREATE TABLE `satuan` (
  `id` int NOT NULL AUTO_INCREMENT,
  `nama` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=latin1;

INSERT INTO `satuan` (`id`, `nama`) VALUES (1, 'Strip');


#
# TABLE STRUCTURE FOR: stok_opname
#

DROP TABLE IF EXISTS `stok_opname`;

CREATE TABLE `stok_opname` (
  `id` int NOT NULL AUTO_INCREMENT,
  `id_transaksi` int DEFAULT NULL,
  `id_barang` int DEFAULT NULL,
  `tanggal` date DEFAULT NULL,
  `transaksi` enum('Stok Opname','Barang Masuk','Barang Keluar','Koreksi Stok','Retur Keluar','Retur Penerimaan') CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `ed` date DEFAULT NULL,
  `nobatch` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `masuk` double NOT NULL DEFAULT '0',
  `keluar` double NOT NULL DEFAULT '0',
  `keterangan` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci,
  `user_input` int DEFAULT NULL,
  `id_gudang` int DEFAULT NULL,
  `tgl_input` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `id_barang_fk` (`id_barang`),
  KEY `stok_opname_ibfk_1` (`id_gudang`),
  CONSTRAINT `stok_opname_ibfk_1` FOREIGN KEY (`id_gudang`) REFERENCES `gudang` (`id`),
  CONSTRAINT `stok_opname_ibfk_2` FOREIGN KEY (`id_barang`) REFERENCES `barang` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=latin1;

#
# TABLE STRUCTURE FOR: supplier
#

DROP TABLE IF EXISTS `supplier`;

CREATE TABLE `supplier` (
  `id` int NOT NULL AUTO_INCREMENT,
  `nama` varchar(255) NOT NULL,
  `no_telepon` varchar(255) NOT NULL,
  `alamat` text NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb3;

INSERT INTO `supplier` (`id`, `nama`, `no_telepon`, `alamat`) VALUES (2, 'supplier 1', '1234567', 'tester');


#
# TABLE STRUCTURE FOR: tbl_akses_menu
#

DROP TABLE IF EXISTS `tbl_akses_menu`;

CREATE TABLE `tbl_akses_menu` (
  `id` int NOT NULL AUTO_INCREMENT,
  `id_level` int NOT NULL,
  `id_menu` int NOT NULL,
  `view` enum('Y','N') CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT 'N',
  `add` enum('Y','N') CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT 'N',
  `edit` enum('Y','N') CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT 'N',
  `delete` enum('Y','N') CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT 'N',
  `print` enum('Y','N') CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT 'N',
  `upload` enum('Y','N') CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT 'N',
  `download` enum('Y','N') CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT 'N',
  PRIMARY KEY (`id`),
  KEY `id_menu` (`id_menu`),
  KEY `id_level` (`id_level`),
  CONSTRAINT `tbl_akses_menu_ibfk_1` FOREIGN KEY (`id_level`) REFERENCES `tbl_userlevel` (`id_level`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `tbl_akses_menu_ibfk_2` FOREIGN KEY (`id_menu`) REFERENCES `tbl_menu` (`id_menu`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=432 DEFAULT CHARSET=latin1;

INSERT INTO `tbl_akses_menu` (`id`, `id_level`, `id_menu`, `view`, `add`, `edit`, `delete`, `print`, `upload`, `download`) VALUES (1, 1, 1, 'Y', 'N', 'N', 'N', 'N', 'N', 'N');
INSERT INTO `tbl_akses_menu` (`id`, `id_level`, `id_menu`, `view`, `add`, `edit`, `delete`, `print`, `upload`, `download`) VALUES (69, 1, 2, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'N');
INSERT INTO `tbl_akses_menu` (`id`, `id_level`, `id_menu`, `view`, `add`, `edit`, `delete`, `print`, `upload`, `download`) VALUES (94, 1, 3, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'N');
INSERT INTO `tbl_akses_menu` (`id`, `id_level`, `id_menu`, `view`, `add`, `edit`, `delete`, `print`, `upload`, `download`) VALUES (207, 1, 4, 'Y', 'N', 'N', 'N', 'N', 'N', 'N');
INSERT INTO `tbl_akses_menu` (`id`, `id_level`, `id_menu`, `view`, `add`, `edit`, `delete`, `print`, `upload`, `download`) VALUES (427, 1, 6, 'Y', 'N', 'N', 'N', 'N', 'N', 'N');
INSERT INTO `tbl_akses_menu` (`id`, `id_level`, `id_menu`, `view`, `add`, `edit`, `delete`, `print`, `upload`, `download`) VALUES (428, 1, 7, 'Y', 'N', 'N', 'N', 'N', 'N', 'N');
INSERT INTO `tbl_akses_menu` (`id`, `id_level`, `id_menu`, `view`, `add`, `edit`, `delete`, `print`, `upload`, `download`) VALUES (429, 1, 8, 'Y', 'Y', 'Y', 'N', 'N', 'N', 'N');
INSERT INTO `tbl_akses_menu` (`id`, `id_level`, `id_menu`, `view`, `add`, `edit`, `delete`, `print`, `upload`, `download`) VALUES (430, 1, 9, 'Y', 'N', 'N', 'N', 'N', 'N', 'N');
INSERT INTO `tbl_akses_menu` (`id`, `id_level`, `id_menu`, `view`, `add`, `edit`, `delete`, `print`, `upload`, `download`) VALUES (431, 1, 10, 'Y', 'N', 'N', 'N', 'N', 'N', 'N');


#
# TABLE STRUCTURE FOR: tbl_akses_submenu
#

DROP TABLE IF EXISTS `tbl_akses_submenu`;

CREATE TABLE `tbl_akses_submenu` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `id_level` int NOT NULL,
  `id_submenu` int NOT NULL,
  `view` enum('Y','N') CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT 'N',
  `add` enum('Y','N') CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT 'N',
  `edit` enum('Y','N') CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT 'N',
  `delete` enum('Y','N') CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT 'N',
  `print` enum('Y','N') CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT 'N',
  `upload` enum('Y','N') CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT 'N',
  `download` enum('Y','N') CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT 'N',
  PRIMARY KEY (`id`),
  KEY `id_level` (`id_level`),
  KEY `id_submenu` (`id_submenu`),
  CONSTRAINT `tbl_akses_submenu_ibfk_1` FOREIGN KEY (`id_level`) REFERENCES `tbl_userlevel` (`id_level`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `tbl_akses_submenu_ibfk_2` FOREIGN KEY (`id_submenu`) REFERENCES `tbl_submenu` (`id_submenu`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=358 DEFAULT CHARSET=latin1;

INSERT INTO `tbl_akses_submenu` (`id`, `id_level`, `id_submenu`, `view`, `add`, `edit`, `delete`, `print`, `upload`, `download`) VALUES (2, 1, 2, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'N');
INSERT INTO `tbl_akses_submenu` (`id`, `id_level`, `id_submenu`, `view`, `add`, `edit`, `delete`, `print`, `upload`, `download`) VALUES (4, 1, 1, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'N');
INSERT INTO `tbl_akses_submenu` (`id`, `id_level`, `id_submenu`, `view`, `add`, `edit`, `delete`, `print`, `upload`, `download`) VALUES (6, 1, 3, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'N');
INSERT INTO `tbl_akses_submenu` (`id`, `id_level`, `id_submenu`, `view`, `add`, `edit`, `delete`, `print`, `upload`, `download`) VALUES (9, 1, 4, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'N');
INSERT INTO `tbl_akses_submenu` (`id`, `id_level`, `id_submenu`, `view`, `add`, `edit`, `delete`, `print`, `upload`, `download`) VALUES (209, 1, 5, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y');
INSERT INTO `tbl_akses_submenu` (`id`, `id_level`, `id_submenu`, `view`, `add`, `edit`, `delete`, `print`, `upload`, `download`) VALUES (289, 1, 6, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y');
INSERT INTO `tbl_akses_submenu` (`id`, `id_level`, `id_submenu`, `view`, `add`, `edit`, `delete`, `print`, `upload`, `download`) VALUES (295, 1, 7, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y');
INSERT INTO `tbl_akses_submenu` (`id`, `id_level`, `id_submenu`, `view`, `add`, `edit`, `delete`, `print`, `upload`, `download`) VALUES (351, 1, 71, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y');
INSERT INTO `tbl_akses_submenu` (`id`, `id_level`, `id_submenu`, `view`, `add`, `edit`, `delete`, `print`, `upload`, `download`) VALUES (352, 1, 72, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y');
INSERT INTO `tbl_akses_submenu` (`id`, `id_level`, `id_submenu`, `view`, `add`, `edit`, `delete`, `print`, `upload`, `download`) VALUES (353, 1, 73, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y');
INSERT INTO `tbl_akses_submenu` (`id`, `id_level`, `id_submenu`, `view`, `add`, `edit`, `delete`, `print`, `upload`, `download`) VALUES (354, 1, 74, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y');
INSERT INTO `tbl_akses_submenu` (`id`, `id_level`, `id_submenu`, `view`, `add`, `edit`, `delete`, `print`, `upload`, `download`) VALUES (355, 1, 75, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y');
INSERT INTO `tbl_akses_submenu` (`id`, `id_level`, `id_submenu`, `view`, `add`, `edit`, `delete`, `print`, `upload`, `download`) VALUES (356, 1, 76, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y');
INSERT INTO `tbl_akses_submenu` (`id`, `id_level`, `id_submenu`, `view`, `add`, `edit`, `delete`, `print`, `upload`, `download`) VALUES (357, 1, 77, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y');


#
# TABLE STRUCTURE FOR: tbl_menu
#

DROP TABLE IF EXISTS `tbl_menu`;

CREATE TABLE `tbl_menu` (
  `id_menu` int NOT NULL AUTO_INCREMENT,
  `nama_menu` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `link` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `icon` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `urutan` bigint DEFAULT NULL,
  `is_active` enum('Y','N') CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT 'Y',
  `parent` enum('Y') CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT 'Y',
  PRIMARY KEY (`id_menu`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=latin1;

INSERT INTO `tbl_menu` (`id_menu`, `nama_menu`, `link`, `icon`, `urutan`, `is_active`, `parent`) VALUES (1, 'Home', 'dashboard', 'fas fa-tachometer-alt', '1', 'Y', 'Y');
INSERT INTO `tbl_menu` (`id_menu`, `nama_menu`, `link`, `icon`, `urutan`, `is_active`, `parent`) VALUES (2, 'Konfigurasi', '#', 'fas fa-users-cog', '15', 'Y', 'Y');
INSERT INTO `tbl_menu` (`id_menu`, `nama_menu`, `link`, `icon`, `urutan`, `is_active`, `parent`) VALUES (3, 'Ganti Password', 'ganti_password', 'fas fa-key', '9', 'Y', 'Y');
INSERT INTO `tbl_menu` (`id_menu`, `nama_menu`, `link`, `icon`, `urutan`, `is_active`, `parent`) VALUES (4, 'Master', '#', 'fas fa-database', '7', 'Y', 'Y');
INSERT INTO `tbl_menu` (`id_menu`, `nama_menu`, `link`, `icon`, `urutan`, `is_active`, `parent`) VALUES (6, 'Developer', '#', 'fas fa-tools', '10', 'Y', 'Y');
INSERT INTO `tbl_menu` (`id_menu`, `nama_menu`, `link`, `icon`, `urutan`, `is_active`, `parent`) VALUES (7, 'Purchase', '#', 'fas fa-credit-card', '3', 'Y', 'Y');
INSERT INTO `tbl_menu` (`id_menu`, `nama_menu`, `link`, `icon`, `urutan`, `is_active`, `parent`) VALUES (8, 'Sales', '#', 'fas fa-store', '1', 'Y', 'Y');
INSERT INTO `tbl_menu` (`id_menu`, `nama_menu`, `link`, `icon`, `urutan`, `is_active`, `parent`) VALUES (9, 'Warehouse', '#', 'fas fa-warehouse', '2', 'Y', 'Y');
INSERT INTO `tbl_menu` (`id_menu`, `nama_menu`, `link`, `icon`, `urutan`, `is_active`, `parent`) VALUES (10, 'Akunting', '#', 'fas fa-receipt', '5', 'Y', 'Y');


#
# TABLE STRUCTURE FOR: tbl_submenu
#

DROP TABLE IF EXISTS `tbl_submenu`;

CREATE TABLE `tbl_submenu` (
  `id_submenu` int NOT NULL AUTO_INCREMENT,
  `nama_submenu` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `link` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `icon` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `id_menu` int DEFAULT NULL,
  `is_active` enum('Y','N') CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT 'Y',
  `urutan` int unsigned DEFAULT NULL,
  PRIMARY KEY (`id_submenu`),
  KEY `id_menu` (`id_menu`),
  CONSTRAINT `tbl_submenu_ibfk_1` FOREIGN KEY (`id_menu`) REFERENCES `tbl_menu` (`id_menu`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=78 DEFAULT CHARSET=latin1;

INSERT INTO `tbl_submenu` (`id_submenu`, `nama_submenu`, `link`, `icon`, `id_menu`, `is_active`, `urutan`) VALUES (1, 'Menu', 'menu', 'far fa-circle', 2, 'Y', NULL);
INSERT INTO `tbl_submenu` (`id_submenu`, `nama_submenu`, `link`, `icon`, `id_menu`, `is_active`, `urutan`) VALUES (2, 'Sub Menu', 'submenu', 'far fa-circle', 2, 'Y', NULL);
INSERT INTO `tbl_submenu` (`id_submenu`, `nama_submenu`, `link`, `icon`, `id_menu`, `is_active`, `urutan`) VALUES (3, 'Aplikasi', 'aplikasi', 'far fa-circle', 2, 'Y', NULL);
INSERT INTO `tbl_submenu` (`id_submenu`, `nama_submenu`, `link`, `icon`, `id_menu`, `is_active`, `urutan`) VALUES (4, 'User Level', 'userlevel', 'far fa-circle', 2, 'Y', NULL);
INSERT INTO `tbl_submenu` (`id_submenu`, `nama_submenu`, `link`, `icon`, `id_menu`, `is_active`, `urutan`) VALUES (5, 'Data Pengguna', 'user', 'far fa-circle', 2, 'Y', NULL);
INSERT INTO `tbl_submenu` (`id_submenu`, `nama_submenu`, `link`, `icon`, `id_menu`, `is_active`, `urutan`) VALUES (6, 'Barang', 'barang', 'far fa-circle', 4, 'Y', NULL);
INSERT INTO `tbl_submenu` (`id_submenu`, `nama_submenu`, `link`, `icon`, `id_menu`, `is_active`, `urutan`) VALUES (7, 'Satuan', 'satuan', 'far fa-circle', 4, 'Y', NULL);
INSERT INTO `tbl_submenu` (`id_submenu`, `nama_submenu`, `link`, `icon`, `id_menu`, `is_active`, `urutan`) VALUES (71, 'Create Tabel', 'TemplateController', 'far fa-circle', 6, 'Y', 1);
INSERT INTO `tbl_submenu` (`id_submenu`, `nama_submenu`, `link`, `icon`, `id_menu`, `is_active`, `urutan`) VALUES (72, 'Gudang', 'gudang', 'far fa-circle', 4, 'Y', 4);
INSERT INTO `tbl_submenu` (`id_submenu`, `nama_submenu`, `link`, `icon`, `id_menu`, `is_active`, `urutan`) VALUES (73, 'Pelanggan', 'pelanggan', 'far fa-circle', 4, 'Y', 5);
INSERT INTO `tbl_submenu` (`id_submenu`, `nama_submenu`, `link`, `icon`, `id_menu`, `is_active`, `urutan`) VALUES (74, 'Suppier', 'supplier', 'far fa-circle', 4, 'Y', 3);
INSERT INTO `tbl_submenu` (`id_submenu`, `nama_submenu`, `link`, `icon`, `id_menu`, `is_active`, `urutan`) VALUES (75, 'Barang Masuk', 'masuk', 'far fa-circle', 9, 'Y', 1);
INSERT INTO `tbl_submenu` (`id_submenu`, `nama_submenu`, `link`, `icon`, `id_menu`, `is_active`, `urutan`) VALUES (76, 'Barang Keluar', 'keluar', 'far fa-circle', 9, 'Y', 2);
INSERT INTO `tbl_submenu` (`id_submenu`, `nama_submenu`, `link`, `icon`, `id_menu`, `is_active`, `urutan`) VALUES (77, 'Stok Opname', 'stok', 'far fa-circle', 9, 'Y', 3);


#
# TABLE STRUCTURE FOR: tbl_user
#

DROP TABLE IF EXISTS `tbl_user`;

CREATE TABLE `tbl_user` (
  `id_user` int NOT NULL AUTO_INCREMENT,
  `username` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `full_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `password` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `id_level` int DEFAULT NULL,
  `image` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `nohp` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `email` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `is_active` enum('Y','N') CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT 'Y',
  PRIMARY KEY (`id_user`),
  KEY `id_level` (`id_level`),
  CONSTRAINT `tbl_user_ibfk_1` FOREIGN KEY (`id_level`) REFERENCES `tbl_userlevel` (`id_level`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=latin1;

INSERT INTO `tbl_user` (`id_user`, `username`, `full_name`, `password`, `id_level`, `image`, `nohp`, `email`, `is_active`) VALUES (1, 'admin', 'Administrator', '$2y$05$Bl1UXpDrO8843SqKlnGkq.AjnPhDIGAbfKAoVUkqpUAp4um3LtrbW', 1, 'admin.jpg', '08129837323', '<EMAIL>', 'Y');


#
# TABLE STRUCTURE FOR: tbl_userlevel
#

DROP TABLE IF EXISTS `tbl_userlevel`;

CREATE TABLE `tbl_userlevel` (
  `id_level` int NOT NULL AUTO_INCREMENT,
  `nama_level` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  PRIMARY KEY (`id_level`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=latin1;

INSERT INTO `tbl_userlevel` (`id_level`, `nama_level`) VALUES (1, 'Administrator');


SET foreign_key_checks = 1;
