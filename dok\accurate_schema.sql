
-- Tabel Customers
CREATE TABLE customers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    code VA<PERSON>HAR(50) NOT NULL,
    name VA<PERSON>HAR(100) NOT NULL,
    address TEXT,
    phone VARCHAR(20),
    email VARCHAR(100),
    npwp VARCHAR(30),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Tabel Sales Orders
CREATE TABLE sales_orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_no VARCHAR(50) NOT NULL,
    customer_id INT,
    order_date DATE NOT NULL,
    due_date DATE,
    status ENUM('draft','approved','delivered','invoiced') DEFAULT 'draft',
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers(id)
);

-- Tabel Sales Order Items
CREATE TABLE sales_order_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    sales_order_id INT,
    product_id INT,
    quantity INT NOT NULL,
    unit_price DECIMAL(15,2) NOT NULL,
    discount DECIMAL(15,2) DEFAULT 0,
    subtotal DECIMAL(15,2) AS (quantity * unit_price - discount) STORED,
    FOREIGN KEY (sales_order_id) REFERENCES sales_orders(id),
    FOREIGN KEY (product_id) REFERENCES products(id)
);

-- Tabel Delivery Orders
CREATE TABLE delivery_orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    delivery_no VARCHAR(50),
    sales_order_id INT,
    delivery_date DATE,
    status ENUM('draft','sent','completed') DEFAULT 'draft',
    notes TEXT,
    FOREIGN KEY (sales_order_id) REFERENCES sales_orders(id)
);

-- Tabel Delivery Items
CREATE TABLE delivery_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    delivery_order_id INT,
    product_id INT,
    quantity INT NOT NULL,
    FOREIGN KEY (delivery_order_id) REFERENCES delivery_orders(id),
    FOREIGN KEY (product_id) REFERENCES products(id)
);

-- Tabel Invoices
CREATE TABLE invoices (
    id INT AUTO_INCREMENT PRIMARY KEY,
    invoice_no VARCHAR(50),
    customer_id INT,
    sales_order_id INT,
    invoice_date DATE,
    due_date DATE,
    total DECIMAL(15,2),
    status ENUM('unpaid','paid','partial') DEFAULT 'unpaid',
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    FOREIGN KEY (sales_order_id) REFERENCES sales_orders(id)
);

-- Tabel Invoice Items
CREATE TABLE invoice_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    invoice_id INT,
    product_id INT,
    quantity INT,
    unit_price DECIMAL(15,2),
    discount DECIMAL(15,2),
    FOREIGN KEY (invoice_id) REFERENCES invoices(id),
    FOREIGN KEY (product_id) REFERENCES products(id)
);

-- Tabel Payments
CREATE TABLE payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    invoice_id INT,
    payment_date DATE,
    amount DECIMAL(15,2),
    method ENUM('cash','transfer','giro','credit_card'),
    notes TEXT,
    FOREIGN KEY (invoice_id) REFERENCES invoices(id)
);

-- Tabel Products
CREATE TABLE products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(50) UNIQUE,
    name VARCHAR(100),
    unit VARCHAR(20),
    price DECIMAL(15,2),
    stock INT DEFAULT 0
);

-- Tabel Warehouses
CREATE TABLE warehouses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100),
    location TEXT
);

-- Tabel Stock Movements
CREATE TABLE stock_movements (
    id INT AUTO_INCREMENT PRIMARY KEY,
    product_id INT,
    warehouse_id INT,
    movement_date DATE,
    type ENUM('in','out','adjustment'),
    reference VARCHAR(100),
    quantity INT,
    notes TEXT,
    FOREIGN KEY (product_id) REFERENCES products(id),
    FOREIGN KEY (warehouse_id) REFERENCES warehouses(id)
);

-- Tabel Suppliers
CREATE TABLE suppliers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100),
    address TEXT,
    phone VARCHAR(20),
    email VARCHAR(100),
    npwp VARCHAR(30)
);

-- Tabel Purchase Orders
CREATE TABLE purchase_orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    po_no VARCHAR(50),
    supplier_id INT,
    order_date DATE,
    status ENUM('draft','ordered','received','invoiced') DEFAULT 'draft',
    notes TEXT,
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id)
);

-- Tabel Purchase Order Items
CREATE TABLE purchase_order_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    purchase_order_id INT,
    product_id INT,
    quantity INT,
    unit_price DECIMAL(15,2),
    FOREIGN KEY (purchase_order_id) REFERENCES purchase_orders(id),
    FOREIGN KEY (product_id) REFERENCES products(id)
);

-- Tabel Purchase Invoices
CREATE TABLE purchase_invoices (
    id INT AUTO_INCREMENT PRIMARY KEY,
    invoice_no VARCHAR(50),
    supplier_id INT,
    po_id INT,
    invoice_date DATE,
    total DECIMAL(15,2),
    status ENUM('unpaid','paid','partial'),
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
    FOREIGN KEY (po_id) REFERENCES purchase_orders(id)
);

-- Tabel Accounts
CREATE TABLE accounts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(20) UNIQUE,
    name VARCHAR(100),
    type ENUM('asset','liability','equity','revenue','expense')
);

-- Tabel Journal Entries
CREATE TABLE journal_entries (
    id INT AUTO_INCREMENT PRIMARY KEY,
    entry_date DATE,
    description TEXT,
    reference VARCHAR(50),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Tabel Journal Details
CREATE TABLE journal_details (
    id INT AUTO_INCREMENT PRIMARY KEY,
    journal_entry_id INT,
    account_id INT,
    debit DECIMAL(15,2) DEFAULT 0,
    credit DECIMAL(15,2) DEFAULT 0,
    FOREIGN KEY (journal_entry_id) REFERENCES journal_entries(id),
    FOREIGN KEY (account_id) REFERENCES accounts(id)
);
