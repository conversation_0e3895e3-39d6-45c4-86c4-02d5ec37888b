<form action="#" id="form_detail" class="form-horizontal">
    <input type="hidden" value="" name="id" />
    <div class="card-body">
        <div class="form-group row">
            <label for="nama_barang" class="col-sm-3 col-form-label"><PERSON><PERSON> <span class="text-danger">*</span></label>
            <div class="col-sm-9 kosong">
                <input type="hidden" class="form-control" name="id_barang" id="id_barang" placeholder="ID Barang" required>
                <input type="text" class="form-control" name="nama_barang" id="nama_barang" placeholder="Nama Barang" autocomplete="off" required>
                <span class="help-block"></span>
            </div>
        </div>
        <div class="form-group row">
            <label for="qty" class="col-sm-3 col-form-label">Qty <span class="text-danger">*</span></label>
            <div class="col-sm-9 kosong">
                <input type="number" class="form-control" name="qty" id="qty" placeholder="Quantity" min="1" step="0.01" required>
                <span class="help-block"></span>
            </div>
        </div>
        <div class="form-group row">
            <label for="harga" class="col-sm-3 col-form-label">Harga <span class="text-danger">*</span></label>
            <div class="col-sm-9 kosong">
                <input type="number" class="form-control" name="harga" id="harga" placeholder="Harga" min="0" step="0.01" required>
                <span class="help-block"></span>
            </div>
        </div>
        <div class="form-group row">
            <label for="keterangan" class="col-sm-3 col-form-label">Keterangan</label>
            <div class="col-sm-9 kosong">
                <textarea class="form-control" name="keterangan" id="keterangan" placeholder="Keterangan" rows="3"></textarea>
                <span class="help-block"></span>
            </div>
        </div>
    </div>
</form>

<script type="text/javascript">
    $(document).ready(function() {
        $("#nama_barang").autocomplete({
            source: '<?= base_url('orders/get_barang') ?>',
            minLength: 2,
            select: function(event, ui) {
                $("#id_barang").val(ui.item.value);
                $("#nama_barang").val(ui.item.nama_barang);
                $("#harga").val(ui.item.harga);
                return false;
            },
            change: function(event, ui) {
                if (ui.item) {
                    $("#id_barang").val(ui.item.value);
                    $("#nama_barang").val(ui.item.nama_barang);
                    $("#harga").val(ui.item.harga);
                } else {
                    // Reset jika tidak ada item yang dipilih
                    $("#id_barang").val('');
                    $("#harga").val('');
                }
                return false;
            }
        });

        // Validasi sebelum submit
        $("#form_detail").on('submit', function(e) {
            if ($("#id_barang").val() == '') {
                e.preventDefault();
                alert('Silakan pilih barang dari daftar autocomplete');
                $("#nama_barang").focus();
                return false;
            }
        });
    });
</script>
