<?php
if ($act == "xls") {
	$filename = 'A<PERSON>_stok_' . date("ymdhis");
	header('Chace-Control: no-store, no-cache, must-revalation');
	header('Chace-Control: post-check=0, pre-check=0', FALSE);
	header('Pragma: no-cache');
	header('Content-Type: application/vnd.ms-excel');
	header('Content-Disposition: attachment; filename="' . $filename . '.xls"');
}

?>
<!DOCTYPE html>
<html>

<head>
	<title>export</title>
</head>

<body>

	<table class="table table-bordered" id="tbl_laptiket" border="1" cellspacing="0" cellpadding="5" width="100%">
		<thead class="bg-primary">
			<tr>
				<th>No</th>
				<th>Transaksi</th>
				<th>Tanggal</th>
				<th>Nama Barang</th>
				<th>Pelanggan</th>
				<th>Supplier</th>
				<th>Masuk</th>
				<th>Keluar</th>
				<th>Sisa</th>
			</tr>
		</thead>
		<tbody>
			<?php $no = 1;
			foreach ($lap->result() as $row):
				$sisa = ($row->masuk - $row->keluar);
			?>
				<tr>
					<td><?= $no++; ?></td>
					<td><?php echo $row->transaksi; ?></td>
					<td><?php echo date("d/m/Y", strtotime($row->tanggal)); ?></td>
					<td><?php echo $row->nama_barang; ?></td>
					<td><?php echo $row->nama_pelanggan; ?></td>
					<td><?php echo $row->nama_supplier; ?></td>
					<td><?php echo $row->masuk; ?></td>
					<td><?php echo $row->keluar; ?></td>
					<td><?php echo $sisa; ?></td>


				</tr>
			<?php endforeach; ?>

		</tbody>
	</table>
	<script type="text/javascript">
		$(document).ready(function() {
			$("#tbl_laptiket").DataTable({
				"responsive": true,
				"autoWidth": false,
			});
		})

		function detail(tgl) {
			$.ajax({
				url: "<?php echo site_url('lapelnusa/lap_pendapatan') ?>",
				data: {
					tgl: tgl
				},
				dataType: 'html',
				type: 'POST',
				success: function(html) {
					$("#load_detail").html(html);
				}
			});
		}
	</script>
</body>

</html>