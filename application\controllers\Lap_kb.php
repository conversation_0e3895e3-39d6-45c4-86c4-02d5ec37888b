<?php
defined('BASEPATH') or exit('No direct script access allowed');

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

/**
 * Create By : Aryo
 * Youtube : Aryo Coding
 */
class Lap_kb extends MY_Controller
{

    function __construct()
    {
        parent::__construct();
        $this->load->model(array('Mod_laporan', 'Mod_supir'));
        // $this->load->model('dashboard/Mod_dashboard');
    }

    public function index()
    {
        $link = $this->uri->segment(1);
        $level = $this->session->userdata('id_level');
        // Cek Posisi Menu apakah Sub Menu Atau bukan
        $jml = $this->Mod_dashboard->get_akses_menu($link, $level)->num_rows();

        if ($jml > 0) { //Jika Menu
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $a_menu = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $akses = $a_menu->view;
        } else {
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $a_submenu = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $akses = $a_submenu->view;
        }
        $data['supir'] = $this->Mod_supir->get_all();
        if ($akses == "Y") {
            $this->template->load('layoutbackend', 'laporan/lap_kb', $data);
        } else {
            $data['page'] = $link;
            $this->template->load('layoutbackend', 'admin/akses_ditolak', $data);
        }
    }

    /*   public function get_brg()
    {
        
       $id = $this->input->get('term');
        $data = $this->Mod_keluar->get_brg($id);
        if (count($data) > 0) {
            foreach ($data as $row) {
                $arr_result[] = array( 'label'  => $row->nama, 'produk_nama'  => $row->nama, 'id_barang' => $row->id, 'produk_harga' =>  $row->harga, 'id_kemasan' => $row->kemasan, 'nama_satuan' => $row->nama_satuan);
            }
            echo json_encode($arr_result);
        }else{
            $arr_result = array( 'produk_nama'  => "Data Tidak di Temukan" );
            echo json_encode($arr_result);
        }

    }*/

    public function laporan()
    {
        $group = $this->input->post('group');
        $tglrange = $this->input->post('tgl');
        $id_pelanggan = $this->input->post('id_pelanggan');

        $data['act'] = $group;
        $data['lap'] = $this->Mod_laporan->get_laporan_kb($tglrange, $id_pelanggan,  $group);
        $this->load->view('laporan/vlap_kb', $data);
    }

    public function cetak()
    {
        $group = $this->input->post('group');
        $tglrange = $this->input->post('tgl');
        $id_pelanggan = $this->input->post('id_pelanggan');
        $data['act'] = $group;
        $data['tgltange'] = $this->input->post('tgl');
        $data['lap'] = $this->Mod_laporan->get_laporan_kb($tglrange, $id_pelanggan, $group);
        $this->load->view('laporan/cetak_kb', $data);
    }

    public function lap_excel()
    {
        $group = $this->input->post('group');
        $tglrange = $this->input->post('tgl');
        $id_pelanggan = $this->input->post('id_pelanggan');
        $id_supir = $this->input->post('id_supir');
        $data['act'] = $group;
        $list = $this->Mod_laporan->get_laporan_kb($tglrange, $id_pelanggan,  $group)->result();
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        // Add a header for the reporting period
        $sheet->mergeCells('A1:K1');
        $sheet->setCellValue('A1', 'Laporan Periode: ' . $tglrange);

        // Set headers
        $sheet->setCellValue('A2', 'No');
        $sheet->setCellValue('B2', 'Tanggal');
        $sheet->setCellValue('C2', 'Pelanggan');
        $sheet->setCellValue('D2', 'Nama Barang');
        $sheet->setCellValue('E2', 'Satuan');
        $sheet->setCellValue('F2', 'Jumlah');
        $sheet->setCellValue('G2', 'Harga Modal');
        $sheet->setCellValue('H2', 'Total Modal');
        $sheet->setCellValue('I2', 'Harga Jual');
        $sheet->setCellValue('J2', 'Total Jual');
        $sheet->setCellValue('K2', 'Pendapatan Bersih');
        $no = 1;
        $x = 3;
        foreach ($list as $row) {

            $tmodal = ($row->harga * $row->jumlah);
            $subt = ($row->harga_jual * $row->jumlah);
            $bersih = $subt - $tmodal;

            $sheet->setCellValue('A' . $x, $no++);
            $sheet->setCellValue('B' . $x, date("d/m/Y", strtotime($row->tanggal)));
            $sheet->setCellValue('C' . $x, $row->nama_pelanggan);
            $sheet->setCellValue('D' . $x, $row->nama_barang);
            $sheet->setCellValue('E' . $x, $row->nama_kemasan);
            $sheet->setCellValue('F' . $x, $row->jumlah);
            $sheet->setCellValue('G' . $x, $row->harga);
            $sheet->setCellValue('H' . $x, $tmodal);
            $sheet->setCellValue('I' . $x, $row->harga_jual);
            $sheet->setCellValue('J' . $x, $subt);
            $sheet->setCellValue('K' . $x, $bersih);

            // Apply Rupiah format to monetary values
            $sheet->getStyle('G' . $x)->getNumberFormat()->setFormatCode('"Rp"#,##0');
            $sheet->getStyle('H' . $x)->getNumberFormat()->setFormatCode('"Rp"#,##0');
            $sheet->getStyle('I' . $x)->getNumberFormat()->setFormatCode('"Rp"#,##0');
            $sheet->getStyle('J' . $x)->getNumberFormat()->setFormatCode('"Rp"#,##0');
            $sheet->getStyle('K' . $x)->getNumberFormat()->setFormatCode('"Rp"#,##0');

            $x++;
        }
        $sheet->setCellValue('E' . $x, 'Total');
        $sheet->setCellValue('F' . $x, '=SUM(F3:F' . ($x - 1) . ')'); // Total of 'Jumlah'
        $sheet->setCellValue('G' . $x, '=SUM(G3:G' . ($x - 1) . ')'); // Total of 'Harga Modal'
        $sheet->setCellValue('H' . $x, '=SUM(H3:H' . ($x - 1) . ')'); // Total of 'Total Modal'
        $sheet->setCellValue('I' . $x, '=SUM(I3:I' . ($x - 1) . ')'); // Total of 'Harga Jual'
        $sheet->setCellValue('J' . $x, '=SUM(J3:J' . ($x - 1) . ')'); // Total of 'Total Jual'
        $sheet->setCellValue('K' . $x, '=SUM(K3:K' . ($x - 1) . ')'); // Total of 'Pendapatan Bersih'

        // Apply Rupiah format to totals
        $sheet->getStyle('G' . $x)->getNumberFormat()->setFormatCode('"Rp"#,##0');
        $sheet->getStyle('H' . $x)->getNumberFormat()->setFormatCode('"Rp"#,##0');
        $sheet->getStyle('I' . $x)->getNumberFormat()->setFormatCode('"Rp"#,##0');
        $sheet->getStyle('J' . $x)->getNumberFormat()->setFormatCode('"Rp"#,##0');
        $sheet->getStyle('K' . $x)->getNumberFormat()->setFormatCode('"Rp"#,##0');


        $writer = new Xlsx($spreadsheet);
        $filename = 'laporan_barang_keluar';

        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="' . $filename . '.xlsx"');
        header('Cache-Control: max-age=0');

        $writer->save('php://output');
    }
}
